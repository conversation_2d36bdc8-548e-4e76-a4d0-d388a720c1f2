using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using CSG.Adapter.Compatability;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using Npgsql;
using Snowflake.Data.Client;
using StandardUtils;
using Z.BulkOperations;
using CSG.Adapter.Configuration;


namespace DBUtils
{
    public class DBUtils
    {
        public string DBConnectionString { get; set; }
        public string DBConnectionStringEncrypted { get; set; }
        public DbConnection DBConnect { get; set; }
        public DbDataAdapter DBDataAdapter { get; set; }
        public DbCommand DBSelectCommand { get; set; }
        public DbCommand DBUpdateCommand { get; set; }
        public DbCommand DBDeleteCommand { get; set; }
        public DbDataReader DBDataReader { get; set; }
        public DataTable ControlData { get; set; }
        private StandardUtils.Utils UCAUtils = new StandardUtils.Utils();
        public string CustomerKeyID { get; set; }
        public CSG.Adapter.Configuration.DatabaseType DBType { get; set; }
        public string DBName { get; set; }
        public string PostgresSchema { get; set; }
        public bool SurpressErrors { get; set; }
        private readonly ILogger? _logger;





        public DBUtils()
        {
        }



        public DBUtils(ILogger logger)
        {
            _logger = logger;
        }

        #nullable enable
        public void Initialize()
        {
            bool Successful = false;

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            DBType = Enum.Parse<CSG.Adapter.Configuration.DatabaseType>(
                UCAUtils.ReadSetting("CSG_SQLDATABASETYPE"), true);

            PostgresSchema = "public";
            if (DBType == DatabaseType.PostgreSQL)
            {
                PostgresSchema = UCAUtils.ReadSetting("CSG_SQLDATABASESCHEMA");

                if (PostgresSchema == "Not Found" || string.IsNullOrEmpty(PostgresSchema))
                {
                    Console.WriteLine("Postgres Schema Not Found in Config. Reverting to public");
                    PostgresSchema = "public";
                }
            }

            Successful = ReadDBConnectionString();

            // Initialize the connection manager if it hasn't been already
            if (!string.IsNullOrEmpty(DBConnectionString))
            {
                try
                {
                    ConnectionManager.Initialize(DBConnectionString, DBType, _logger);
                }
                catch (Exception ex)
                {
                    if (_logger != null)
                        _logger.LogWarning(ex, "Failed to initialize connection manager");
                    else
                        Console.WriteLine($"Failed to initialize connection manager: {ex.Message}");
                }
            }

            string SelectString = "SELECT * from tabledefinitions";
            if (Successful == true)
            {
                try
                {
                    // Get the control data
                    ControlData = GetSQLTableData(SelectString, "ControlData");
                }
                catch (Npgsql.PostgresException ex) when (ex.Message.StartsWith("42P01: relation ")) // does not exist
                {
                    // Try to show some useful information about the schema and search path which is a common cause of
                    // this exception.
                    SelectString = "SELECT setting, current_schema(), current_database() FROM pg_settings WHERE name = 'search_path'";
                    DataTable res = GetSQLTableData(SelectString, "SearchPath");
                    Console.WriteLine(
                        "Cannot access table definitions, check schema/search path or make sure an install has been performed.");
                    if (res != null && res.Rows.Count > 0)
                        Console.WriteLine(
                            "Postgres search path is {0}, schema is {1}, database is {2}",
                            res.Rows[0]["setting"],
                            res.Rows[0]["current_schema"],
                            res.Rows[0]["current_database"]);
                    throw;
                }
                catch
                {
                    Console.WriteLine("Cannot Access Table Definitions - Exiting");
                    throw;
                }
            }
            else
            {
                Console.WriteLine("DB UTILS: Warning:Definition Table not yet Initialized - Might Be Install - If seen in production please contact UCA");
            }

            Z.BulkOperations.LicenseManager.AddLicense("4121;300-ucarchitects.com.au", "b23237d2-d300-881d-098f-020f9e7bdd1c");

            if (!LicenseManager.ValidateLicense(out string licenseErrorMessage))
            {
                throw new ApplicationException(licenseErrorMessage);
            }
        }

        public DateTime GetSyncLastUpdate(string SyncType)
        {
            var LastSyncDate = DateTime.MinValue;
            var utcNow = DateTime.UtcNow;
            SyncType = SyncType.ToLower();

            try
            {
                // Use the in-memory ControlData instead of querying the database again
                if (ControlData != null)
                {
                    // Check version first
                    VersionNumberCompare(SyncType);

                    // Find the row for this sync type
                    DataRow[] rows = ControlData.Select($"tablename = '{SyncType}'");
                    if (rows.Length > 0 && rows[0]["datekeyfield"] != DBNull.Value)
                    {
                        LastSyncDate = Convert.ToDateTime(rows[0]["datekeyfield"]);
                        // Ensure the DateTime has the correct Kind since it's stored as UTC in the database
                        LastSyncDate = DateTime.SpecifyKind(LastSyncDate, DateTimeKind.Utc);
                        _logger?.LogInformation(
                            "DB:Sync: Job {SyncType} last update is {LastUpdate}Z",
                            SyncType,
                            LastSyncDate.ToString("s"));
                    }
                }
                else
                {
                    _logger?.LogWarning("DB:Sync: ControlData is null, cannot get sync date for {SyncType}", SyncType);
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "Suppressed error");
                else
                    Console.WriteLine(ex.ToString());
            }

            if (LastSyncDate == DateTime.MinValue)
            {
                // For audit-related jobs, use 335 days (11 months) instead of 365 days (12 months)
                // to avoid exceeding the maximum retention range of Genesys Cloud audit API
                bool isAuditJob = SyncType.Contains("audit") || SyncType.Contains("wfmaudit") || SyncType.Contains("queueaudit");
                int daysToSubtract = isAuditJob ? 335 : 365;

                LastSyncDate = DateTime.UtcNow.AddDays(-daysToSubtract);

                // Make it VERY clear in the logs that fallback is being used
                _logger?.LogWarning("⚠️  FALLBACK SYNC DATE USED ⚠️");
                _logger?.LogWarning("Table '{SyncType}' not found in tabledefinitions or has NULL datekeyfield", SyncType);
                _logger?.LogWarning("Using fallback sync date: {FallbackDate:yyyy-MM-ddTHH:mm:ss.fffZ} (UTC Now - {Days} days)", LastSyncDate, daysToSubtract);
                _logger?.LogWarning("This will sync data from {Days} days ago instead of the last known sync point", daysToSubtract);

                if (isAuditJob)
                {
                    _logger?.LogInformation(
                        "Audit job {SyncType} using reduced lookback of {Days} days instead of 365 to avoid exceeding maximum retention range",
                        SyncType,
                        daysToSubtract);
                }

                Console.WriteLine(
                    "{0} {1}: ⚠️  FALLBACK: Sync job {2} was not set in tabledefinitions. Using fallback sync date: {3}Z (UTC Now - {4} days)",
                    DateTime.Now.ToString("s"),
                    nameof(GetSyncLastUpdate),
                    SyncType,
                    LastSyncDate.ToString("s"),
                    isAuditJob ? 335 : 365);
            }
            else
            {
                // For existing audit jobs, ensure the date is not more than 335 days in the past
                bool isAuditJob = SyncType.Contains("audit") || SyncType.Contains("wfmaudit") || SyncType.Contains("queueaudit");
                if (isAuditJob)
                {
                    DateTime minAllowedDate = DateTime.UtcNow.AddDays(-335);
                    if (LastSyncDate < minAllowedDate)
                    {
                        _logger?.LogWarning(
                            "Audit job {SyncType} last update {LastUpdate}Z is more than 335 days in the past. Adjusting to {NewDate}Z to avoid exceeding maximum retention range",
                            SyncType,
                            LastSyncDate.ToString("s"),
                            minAllowedDate.ToString("s"));

                        LastSyncDate = minAllowedDate;
                    }
                }
            }

            if (LastSyncDate > utcNow)
            {
                Console.WriteLine(
                    "{0} {1}: Sync job {2} last update {3}Z is in the future. Setting to {4}Z",
                    DateTime.Now.ToString("s"),
                    nameof(GetSyncLastUpdate),
                    SyncType,
                    LastSyncDate.ToString("s"),
                    utcNow.ToString("s"));
                LastSyncDate = utcNow;
            }

            return LastSyncDate;
        }
        public void VersionNumberCompare(string SyncType)
        {
            try
            {
                if (ControlData != null)
                {
                    // Find the row for this sync type
                    DataRow[] rows = ControlData.Select($"tablename = '{SyncType}'");

                    if (rows.Length > 0 && rows[0]["version"] != DBNull.Value)
                    {
                        string LastVersion = Convert.ToString(rows[0]["version"]);
                        string CurrentVersion = ApplicationVersion.MajorMinorPatch.ToString();

                        Version? lastVersion = Version.TryParse(LastVersion, out Version parsedLastVersion) ? parsedLastVersion : null;
                        Version? currentVersion = Version.TryParse(CurrentVersion, out Version parsedCurrentVersion) ? parsedCurrentVersion : null;

                        if (lastVersion != null && currentVersion != null && lastVersion < currentVersion)
                        {
                            _logger?.LogDebug("DB:Version: Table version for {SyncType} ({LastVersion}) is older than current version ({CurrentVersion})",
                                SyncType, LastVersion, CurrentVersion);
                            // We're not throwing an exception here as per the original code
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    _logger.LogError(ex, "DB:Version: Error comparing versions for {SyncType}", SyncType);
                }
                else
                {
                    Console.WriteLine(ex.ToString());
                }
                Environment.Exit(1);
            }
        }
        public bool SetSyncLastUpdate(DateTime UpdateDate, string SyncType)
        {
            string TableName = "tabledefinitions";
            SyncType = SyncType.ToLower();
            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            if (UpdateDate > DateTime.UtcNow && UpdateDate != DateTime.MaxValue)
            {
                _logger?.LogWarning("DB:Sync: UpdateDate {UpdateDate} is in the future, adjusting to current UTC time {UtcNow}",
                    UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                UpdateDate = DateTime.UtcNow;
            }

            _logger?.LogDebug("DB:Sync: Attempting to update sync date for {SyncType} to {UpdateDate}",
                SyncType, UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));

            var sql = string.Format(
                "UPDATE {0} SET datekeyfield = '{1}' WHERE tablename = '{2}'",
                TableName,
                UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                SyncType
            );

            int rowsAffected = ExecuteSqlNonQuery(sql);
            if (rowsAffected == 0)
            {
                _logger?.LogWarning("DB:Sync: No existing row found for {SyncType}, attempting to insert new row", SyncType);
                Console.WriteLine(
                    "{0} {1}: [WARN ] Sync job {2} last update was not set",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType);
                sql = string.Format(
                    "INSERT INTO {0} (datekeyfield, tablename) VALUES ('{1}', '{2}')",
                    TableName,
                    UpdateDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    SyncType
                );
                rowsAffected = ExecuteSqlNonQuery(sql);
            }

            // Update the in-memory ControlData to keep it in sync
            if (rowsAffected > 0 && ControlData != null)
            {
                DataRow[] rows = ControlData.Select($"tablename = '{SyncType}'");
                if (rows.Length > 0)
                {
                    rows[0]["datekeyfield"] = UpdateDate;
                    _logger?.LogDebug("DB:Sync: Updated in-memory ControlData for {SyncType}", SyncType);
                }
                else
                {
                    // Add a new row if it doesn't exist
                    DataRow newRow = ControlData.NewRow();
                    newRow["tablename"] = SyncType;
                    newRow["datekeyfield"] = UpdateDate;
                    ControlData.Rows.Add(newRow);
                    _logger?.LogDebug("DB:Sync: Added new row to in-memory ControlData for {SyncType}", SyncType);
                }
            }
            if (rowsAffected > 0)
            {
                Console.WriteLine(
                    "{0} {1}: Sync job {2} last update set to {3}Z",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType,
                    UpdateDate.ToString("s"));
            }
            else
            {
                Console.WriteLine(
                    "{0} {1}: [ERROR] Failed to set sync job {2} last update to {3}Z",
                    DateTime.Now.ToString("s"),
                    nameof(SetSyncLastUpdate),
                    SyncType,
                    UpdateDate.ToString("s"));
            }

            return rowsAffected > 0;
        }

        public DataTable GetSQLTableSchema(string TableName)
        {
            string sql = "";

            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    sql = "SELECT TOP (0) * FROM " + TableName;
                    break;
                case DatabaseType.MySQL:
                case DatabaseType.PostgreSQL:
                case DatabaseType.Snowflake:
                    sql = "SELECT * FROM " + TableName + " LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DataTable? table = GetSQLTableData(sql, TableName);
            if (table is null)
                throw new DataException("Failed to retrieve table schema for " + TableName);

            return table;
        }

        /// <summary>
        /// Gets data from a SQL table
        /// </summary>
        /// <param name="SQLCommand">The SQL command to execute</param>
        /// <param name="TableName">The name of the table</param>
        /// <returns>A DataTable containing the query results</returns>
        public DataTable GetSQLTableData(string SQLCommand, string TableName)
        {
            DataTable DTTempData = new DataTable();
            DateTime BeforeGet = DateTime.UtcNow;

            ConnectToDatabase();
            try
            {
                DBSelectCommand = DBConnect.CreateCommand();
                DBSelectCommand.CommandText = SQLCommand;
                DBDataAdapter.SelectCommand = DBSelectCommand;
                DBDataAdapter.Fill(DTTempData);
                DBDataAdapter.FillSchema(DTTempData, SchemaType.Source);
                DTTempData.TableName = TableName;

                int rowCount = DTTempData.Rows.Count;
                double elapsedTime = (DateTime.UtcNow - BeforeGet).TotalSeconds;

                // Extract actual table name from SQL query for consistent logging
                string logTableName = ExtractTableNameFromQuery(SQLCommand, TableName);

                // Log using structured logging if available, otherwise fall back to console
                if (_logger != null)
                {
                    // Log a concise message at Information level
                    _logger.LogInformation("DB:Query: Retrieved {RowCount} rows from table '{TableName}'. Duration: {Duration:N3} secs",
                        rowCount, logTableName, elapsedTime);

                    // Log the full query at Debug level for troubleshooting
                    if (_logger.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug))
                        _logger.LogDebug("DB:Query: SQL command for {TableName}: {SQLCommand}", logTableName, SQLCommand);
                }
                else
                {
                    // Fallback to console output when no logger is available
                    Console.WriteLine($"Retrieved {rowCount} rows from table '{logTableName}' using query: '{SQLCommand}'. Duration: {elapsedTime:N3} secs");
                }
            }
            catch (Exception ex)
            {
                string logTableName = DBType == DatabaseType.PostgreSQL ? TableName.ToLower() : TableName;
                if (_logger != null) {
                    _logger.LogError(ex, "DB:Error: Failed query on {TableName}. Query: {Query}", logTableName, SQLCommand);
                }
                else {
                    Console.WriteLine($"Exception while running query '{SQLCommand}' on table '{logTableName}'");
                    Console.WriteLine(ex.ToString());
                }
            }
            finally
            {
                // Return the connection to the pool by closing it
                // This doesn't actually close the connection but returns it to the pool
                if (DBConnect?.State == ConnectionState.Open)
                {
                    _logger?.LogDebug("Returning database connection to the pool after query");
                    DBConnect.Close();
                }
            }

            return DTTempData;
        }

        /// <summary>
        /// Extracts the actual table name used in the SQL query for consistent logging
        /// </summary>
        /// <param name="sqlCommand">The SQL command being executed</param>
        /// <param name="fallbackTableName">The fallback table name if extraction fails</param>
        /// <returns>The table name that should be used in log messages</returns>
        private string ExtractTableNameFromQuery(string sqlCommand, string fallbackTableName)
        {
            if (string.IsNullOrEmpty(sqlCommand))
                return fallbackTableName;

            try
            {
                // For PostgreSQL, we need to handle schema-qualified table names
                if (DBType == DatabaseType.PostgreSQL)
                {
                    // Look for patterns like "FROM schema.tablename" or "FROM tablename"
                    var fromMatch = System.Text.RegularExpressions.Regex.Match(
                        sqlCommand,
                        @"FROM\s+(?:(\w+)\.)?(\w+)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    if (fromMatch.Success)
                    {
                        // If we have a schema prefix, use the full qualified name
                        if (!string.IsNullOrEmpty(fromMatch.Groups[1].Value))
                        {
                            return $"{fromMatch.Groups[1].Value}.{fromMatch.Groups[2].Value}";
                        }
                        // Otherwise just return the table name
                        return fromMatch.Groups[2].Value;
                    }
                }
                else
                {
                    // For other database types, extract table name after FROM
                    var fromMatch = System.Text.RegularExpressions.Regex.Match(
                        sqlCommand,
                        @"FROM\s+(\w+)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    if (fromMatch.Success)
                    {
                        return fromMatch.Groups[1].Value;
                    }
                }
            }
            catch (Exception ex)
            {
                // If regex fails for any reason, log it and fall back to the original table name
                _logger?.LogTrace(ex, "Failed to extract table name from SQL query: {SQLCommand}", sqlCommand);
            }

            // Fallback: For PostgreSQL, convert to lowercase to match the actual query behavior
            return DBType == DatabaseType.PostgreSQL ? fallbackTableName.ToLower() : fallbackTableName;
        }

#nullable restore

        public bool WriteRealSQLData(ref DataTable DTTempData, String TableName)
        {
            bool Successful = false;

            ConnectToDatabase();
            try
            {
                DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();
                if (DRControl == null)
                {
                    Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                    return false;
                }

                string KeyId = DRControl["keyfield"].ToString();

                int AddedRows = 0;
                int ChangedRows = 0;
                int TotalRows = 0;

                DataTable DTTempInsTable = DTTempData.Clone();
                DataTable DTTempUpdTable = DTTempData.Clone();

                foreach (DataRow DRChanges in DTTempData.Rows)
                {

                    TotalRows++;
                    if (DRChanges.RowState == DataRowState.Added)
                    {
                        DRChanges["updated"] = DateTime.UtcNow;
                        DRChanges.AcceptChanges();
                        DRChanges.SetAdded();
                        DTTempInsTable.ImportRow(DRChanges);
                        AddedRows++;
                        if (AddedRows % 100 == 0)
                            Console.Write("+");
                    }
                    else if (DRChanges.RowState == DataRowState.Modified)
                    {
                        DRChanges["updated"] = DateTime.UtcNow;
                        DRChanges.AcceptChanges();
                        DRChanges.SetModified();
                        DTTempUpdTable.ImportRow(DRChanges);
                        ChangedRows++;
                        if (ChangedRows % 100 == 0)
                            Console.Write("*");
                    }
                    else
                    {
                        if (TotalRows % 10 == 0)
                            Console.Write("N");
                    }
                }

                Console.WriteLine("\nData Insert\n");

                var bulk = new BulkOperation(DBConnect)
                {
                    DestinationTableName = TableName
                };

                foreach (DataColumn DCChangedRows in DTTempData.Columns)
                {
                    if (DCChangedRows.ColumnName == KeyId)
                        bulk.ColumnMappings.Add(DCChangedRows.ColumnName, true);
                    else
                        bulk.ColumnMappings.Add(DCChangedRows.ColumnName);
                }

                if (ChangedRows > 0)
                {
                    Console.WriteLine("UR{0}", DTTempUpdTable.Rows.Count);
                    bulk.BulkUpdate(DTTempUpdTable);
                }
                if (AddedRows > 0)
                {
                    Console.WriteLine("IR{0}", DTTempInsTable.Rows.Count);
                    DTTempInsTable.TableName = TableName;
                    bulk.BulkInsert(DTTempInsTable);
                }

            }
            catch
            {
                Console.WriteLine("DB Write Real Time Error");
                throw;
            }

            return Successful;
        }
        /// <summary>
        /// Gets the optimal batch size for database operations based on database type
        /// </summary>
        /// <param name="operationType">Type of operation (bulk, merge, etc.)</param>
        /// <returns>Optimal batch size for the current database type</returns>
        private int GetOptimalBatchSize(string operationType = "default")
        {
            switch (DBType)
            {
                case DatabaseType.Snowflake:
                    // Snowflake performs better with smaller batches due to SQL statement size limitations
                    return operationType.ToLower() switch
                    {
                        "merge" => 500,      // Very small for MERGE operations due to massive SQL generation
                        "bulk" => 1000,      // Small for bulk operations
                        "insert" => 1000,    // Small for insert operations
                        "update" => 1000,    // Small for update operations
                        _ => 1000            // Default small batch size
                    };
                case DatabaseType.PostgreSQL:
                    // PostgreSQL handles larger batches well with bulk operations
                    return operationType.ToLower() switch
                    {
                        "merge" => 5000,
                        "bulk" => 10000,
                        _ => 5000
                    };
                case DatabaseType.MSSQL:
                    // MSSQL performs well with medium to large batches
                    return operationType.ToLower() switch
                    {
                        "merge" => 5000,
                        "bulk" => 10000,
                        _ => 5000
                    };
                case DatabaseType.MySQL:
                    // MySQL performs well with medium batches
                    return operationType.ToLower() switch
                    {
                        "merge" => 2000,
                        "bulk" => 5000,
                        _ => 2000
                    };
                default:
                    return 1000; // Conservative default
            }
        }

        public bool WriteSQLDataBulk(DataTable DTTempData, String TableName)
        {
            // In case DB is Snowflake, redirect to custom built Bulk Insert/Update
            if (DBType == DatabaseType.Snowflake)
            {
                return WriteSQLData(DTTempData, TableName);
            }

            if (DTTempData is null || DTTempData.Rows.Count == 0)
            {
                _logger?.LogInformation("Bulk Upsert for table '{0}' completed - No data to process", TableName);
                Console.WriteLine("Bulk Upsert for table '{0}' completed - No data to process", TableName);
                return true;
            }

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            DateTime BeforeGet = DateTime.UtcNow;

            foreach (DataRow DRRow in DTTempData.Rows)
            {
                DRRow["updated"] = DateTime.UtcNow;
            }

            DateTime AfterGet = DateTime.UtcNow;
            Console.WriteLine("Updating updated field {0}", (AfterGet - BeforeGet));

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            ConnectToDatabase();

            int MaxRowsToSend = GetOptimalBatchSize("bulk");
            int currentPage = 1;

            int totalPages = 0;
            //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

            if (DTTempData.Rows.Count % MaxRowsToSend == 0)
            {
                Console.WriteLine("Reading Block of Data :Equal Division Pages is not adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
            }
            else
            {
                Console.WriteLine("Reading Block of Data :Not Equal Division Pages adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
            }

            while (currentPage <= totalPages)
            {
                DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = DTTempData.TableName;
                //dtTemp.WriteXml(TableName + "Test.XML");

                Console.WriteLine("Processing Rows Block - {0} ", currentPage);
                var bulk = new BulkOperation(DBConnect);
                bulk.DestinationTableName = TableName;
                bulk.BatchTimeout = 600;
                foreach (DataColumn DTTempCol in dtTemp.Columns)
                {
                    //if (DTTempCol.ColumnName != "updated")
                    bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                }

                List<string> PrimaryKeys = KeyId.Split(',').ToList();

                bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                Console.WriteLine("Merging Rows Block - {0} ", currentPage);

                try
                {
                    bulk.BulkMerge(dtTemp);

                    AfterGet = DateTime.UtcNow;
                    if(currentPage == totalPages)
                    {
                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempData.Rows.Count , DTTempData.Rows.Count);
                    }
                    else
                    {
                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);
                    }
                }
                catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "23505") // Duplicate key violation
                {
                    // Log the error with details
                    string errorMessage = $"Duplicate key violation in table {TableName}: {pgEx.Message}";
                    _logger?.LogError(pgEx, errorMessage);
                    Console.WriteLine(errorMessage);

                    // Try to identify the problematic records
                    string constraintName = pgEx.ConstraintName ?? "unknown_constraint";

                    // For partitioned tables like detailedinteractiondata_p2024_05
                    if (constraintName.Contains("_pkey"))
                    {
                        _logger?.LogWarning("Detected duplicate key in partitioned table {TableName} with constraint {Constraint}",
                            TableName, constraintName);

                        // Try a different approach - use BulkUpdate followed by BulkInsert instead of BulkMerge
                        try
                        {
                            _logger?.LogInformation("Attempting alternative approach with separate update and insert operations");

                            // First update existing records
                            bulk.BulkUpdate(dtTemp);

                            // Then try to insert new records, which might still fail if there are true duplicates
                            try
                            {
                                bulk.BulkInsert(dtTemp);
                                _logger?.LogInformation("Successfully completed separate update and insert operations");
                            }
                            catch (Npgsql.PostgresException insertEx) when (insertEx.SqlState == "23505")
                            {
                                _logger?.LogWarning(insertEx, "Some records could not be inserted due to duplicate keys, but updates were processed");

                                // Final fallback: Process records one by one to ensure no data is lost
                                _logger?.LogInformation("Falling back to row-by-row processing to ensure no data is lost");

                                // Get the primary key columns
                                List<string> primaryKeys = KeyId.Split(',').ToList();

                                // Process each row individually
                                int successCount = 0;
                                int failCount = 0;

                                foreach (DataRow row in dtTemp.Rows)
                                {
                                    try
                                    {
                                        // Create a new DataTable with just this row
                                        DataTable singleRowTable = dtTemp.Clone();
                                        singleRowTable.ImportRow(row);

                                        // Try to update first
                                        try
                                        {
                                            using (var singleBulk = new BulkOperation(DBConnect))
                                            {
                                                singleBulk.DestinationTableName = TableName;
                                                singleBulk.BatchTimeout = 60;
                                                foreach (DataColumn col in singleRowTable.Columns)
                                                {
                                                    singleBulk.ColumnMappings.Add(col.ColumnName);
                                                }
                                                singleBulk.ColumnPrimaryKeyNames = primaryKeys;

                                                // Try update first
                                                singleBulk.BulkUpdate(singleRowTable);

                                                // If update didn't throw, try insert
                                                try
                                                {
                                                    singleBulk.BulkInsert(singleRowTable);
                                                }
                                                catch (Npgsql.PostgresException)
                                                {
                                                    // If insert fails, the record likely already exists, which is fine
                                                    // The update should have applied any changes
                                                }
                                            }
                                            successCount++;
                                        }
                                        catch (Exception rowEx)
                                        {
                                            _logger?.LogWarning(rowEx, "Failed to process row with key {Key}",
                                                string.Join(", ", primaryKeys.Select(pk => row[pk]?.ToString() ?? "null")));
                                            failCount++;
                                        }
                                    }
                                    catch (Exception rowEx)
                                    {
                                        _logger?.LogError(rowEx, "Error during row-by-row processing");
                                        failCount++;
                                    }
                                }

                                _logger?.LogInformation("Row-by-row processing completed: {Success} succeeded, {Failed} failed",
                                    successCount, failCount);

                                if (failCount > 0)
                                {
                                    _logger?.LogWarning("Some rows could not be processed even with row-by-row approach. This requires investigation.");
                                }
                            }

                            AfterGet = DateTime.UtcNow;
                            Console.WriteLine("Alternative bulk operation completed for page {0} in {1:N3} secs",
                                currentPage, (AfterGet - BeforeGet).TotalSeconds);
                        }
                        catch (Exception altEx)
                        {
                            _logger?.LogError(altEx, "Alternative bulk operation approach also failed");

                            // Don't throw - try one more approach to ensure no data is lost
                            _logger?.LogInformation("Attempting direct SQL approach as final fallback");

                            try
                            {
                                // Get the primary key columns
                                List<string> primaryKeys = KeyId.Split(',').ToList();

                                // Use direct SQL for the most problematic records
                                int directSqlSuccessCount = 0;

                                foreach (DataRow row in dtTemp.Rows)
                                {
                                    try
                                    {
                                        // Build WHERE clause for primary keys
                                        List<string> whereConditions = new List<string>();
                                        foreach (string pk in primaryKeys)
                                        {
                                            if (row[pk] == DBNull.Value)
                                            {
                                                whereConditions.Add($"{pk} IS NULL");
                                            }
                                            else if (row[pk] is string)
                                            {
                                                whereConditions.Add($"{pk} = '{row[pk].ToString().Replace("'", "''")}'");
                                            }
                                            else if (row[pk] is DateTime)
                                            {
                                                DateTime dt = (DateTime)row[pk];
                                                whereConditions.Add($"{pk} = '{dt:yyyy-MM-dd HH:mm:ss.fff}'");
                                            }
                                            else
                                            {
                                                whereConditions.Add($"{pk} = {row[pk]}");
                                            }
                                        }

                                        string whereClause = string.Join(" AND ", whereConditions);

                                        // Check if record exists
                                        string checkSql = $"SELECT COUNT(*) FROM {TableName} WHERE {whereClause}";
                                        int count = Convert.ToInt32(ExecuteScalar(checkSql));

                                        if (count > 0)
                                        {
                                            // Record exists, update it
                                            List<string> setClauses = new List<string>();
                                            foreach (DataColumn col in dtTemp.Columns)
                                            {
                                                // Skip primary key columns
                                                if (primaryKeys.Contains(col.ColumnName))
                                                    continue;

                                                if (row[col] == DBNull.Value)
                                                {
                                                    setClauses.Add($"{col.ColumnName} = NULL");
                                                }
                                                else if (row[col] is string)
                                                {
                                                    setClauses.Add($"{col.ColumnName} = '{row[col].ToString().Replace("'", "''")}'");
                                                }
                                                else if (row[col] is DateTime)
                                                {
                                                    DateTime dt = (DateTime)row[col];
                                                    setClauses.Add($"{col.ColumnName} = '{dt:yyyy-MM-dd HH:mm:ss.fff}'");
                                                }
                                                else
                                                {
                                                    setClauses.Add($"{col.ColumnName} = {row[col]}");
                                                }
                                            }

                                            string setClause = string.Join(", ", setClauses);
                                            string updateSql = $"UPDATE {TableName} SET {setClause} WHERE {whereClause}";

                                            ExecuteSqlNonQuery(updateSql);
                                        }
                                        else
                                        {
                                            // Record doesn't exist, insert it
                                            List<string> columns = new List<string>();
                                            List<string> values = new List<string>();

                                            // Also prepare update clauses in case we need them
                                            List<string> updateClauses = new List<string>();

                                            foreach (DataColumn col in dtTemp.Columns)
                                            {
                                                columns.Add(col.ColumnName);

                                                // Skip primary key columns for update clauses
                                                if (!primaryKeys.Contains(col.ColumnName))
                                                {
                                                    string valueStr;
                                                    if (row[col] == DBNull.Value)
                                                    {
                                                        valueStr = "NULL";
                                                    }
                                                    else if (row[col] is string)
                                                    {
                                                        valueStr = $"'{row[col].ToString().Replace("'", "''")}'";
                                                    }
                                                    else if (row[col] is DateTime)
                                                    {
                                                        DateTime dt = (DateTime)row[col];
                                                        valueStr = $"'{dt:yyyy-MM-dd HH:mm:ss.fff}'";
                                                    }
                                                    else
                                                    {
                                                        valueStr = row[col].ToString();
                                                    }

                                                    updateClauses.Add($"{col.ColumnName} = {valueStr}");
                                                }

                                                // For values list (INSERT)
                                                if (row[col] == DBNull.Value)
                                                {
                                                    values.Add("NULL");
                                                }
                                                else if (row[col] is string)
                                                {
                                                    values.Add($"'{row[col].ToString().Replace("'", "''")}'");
                                                }
                                                else if (row[col] is DateTime)
                                                {
                                                    DateTime dt = (DateTime)row[col];
                                                    values.Add($"'{dt:yyyy-MM-dd HH:mm:ss.fff}'");
                                                }
                                                else
                                                {
                                                    values.Add(row[col].ToString());
                                                }
                                            }

                                            string insertSql = $"INSERT INTO {TableName} ({string.Join(", ", columns)}) VALUES ({string.Join(", ", values)})";

                                            try
                                            {
                                                ExecuteSqlNonQuery(insertSql);
                                            }
                                            catch (Npgsql.PostgresException ex) when (ex.SqlState == "23505")
                                            {
                                                // If insert fails with duplicate key, try update again
                                                // This can happen if another process inserted the record between our check and insert
                                                string updateSql = $"UPDATE {TableName} SET {string.Join(", ", updateClauses)} WHERE {whereClause}";
                                                ExecuteSqlNonQuery(updateSql);
                                            }
                                        }

                                        directSqlSuccessCount++;
                                    }
                                    catch (Exception rowEx)
                                    {
                                        _logger?.LogError(rowEx, "Failed to process row with direct SQL approach");
                                    }
                                }

                                _logger?.LogInformation("Direct SQL approach completed: {Success} rows processed successfully",
                                    directSqlSuccessCount);
                            }
                            catch (Exception sqlEx)
                            {
                                _logger?.LogError(sqlEx, "Direct SQL approach failed");
                                throw; // Finally give up and throw if all approaches fail
                            }
                        }
                    }
                    else
                    {
                        // For non-partitioned tables, try a simpler approach
                        try
                        {
                            _logger?.LogInformation("Attempting alternative approach for non-partitioned table");

                            // First update existing records
                            bulk.BulkUpdate(dtTemp);

                            // Then try to insert new records
                            try
                            {
                                bulk.BulkInsert(dtTemp);
                            }
                            catch (Npgsql.PostgresException insertEx) when (insertEx.SqlState == "23505")
                            {
                                _logger?.LogWarning(insertEx, "Some records could not be inserted due to duplicate keys, but updates were processed");
                            }

                            AfterGet = DateTime.UtcNow;
                            Console.WriteLine("Alternative operation completed for page {0} in {1:N3} secs",
                                currentPage, (AfterGet - BeforeGet).TotalSeconds);
                        }
                        catch (Exception altEx)
                        {
                            _logger?.LogError(altEx, "Alternative approach failed");
                            throw; // Re-throw if the alternative approach also fails
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Database write failed for table {TableName}: {Message}", TableName, ex.Message);
                    throw;
                }
                currentPage++;
            }

            AfterGet = DateTime.UtcNow;
            Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);

            // Return the connection to the pool by closing it
            // This doesn't actually close the connection but returns it to the pool
            if (DBConnect?.State == ConnectionState.Open)
            {
                _logger?.LogDebug("Returning database connection to the pool after bulk operation");
                DBConnect.Close();
            }
            Console.WriteLine("Connection returned to the pool");
            return true;
        }



        public bool WriteSQLDataBulkWithDeletion(DataTable DTTempData, String TableName)
        {
            if (DBType == DatabaseType.Snowflake)
                return WriteSQLData(DTTempData, TableName);

            if (DTTempData is null)
            {
                Console.WriteLine("Bulk Upsert Completed - Data Was Null");
                return true;
            }

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            DateTime BeforeGet = DateTime.UtcNow;

            foreach (DataRow DRRow in DTTempData.Rows)
            {
                DRRow["updated"] = DateTime.UtcNow;
            }

            DateTime AfterGet = DateTime.UtcNow;
            Console.WriteLine("Updating updated field {0}", (AfterGet - BeforeGet));

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            ConnectToDatabase();

            int MaxRowsToSend = GetOptimalBatchSize("bulk");
            int currentPage = 1;

            int totalPages = 0;
            //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

            StringBuilder idsNotToDeleteBuilder = new StringBuilder();

            if (DTTempData.Rows.Count % MaxRowsToSend == 0)
            {
                Console.WriteLine("Reading Block of Data :Equal Division Pages is not adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
            }
            else
            {
                Console.WriteLine("Reading Block of Data :Not Equal Division Pages adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
            }

            while (currentPage <= totalPages)
            {
                DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = DTTempData.TableName;
                //dtTemp.WriteXml(TableName + "Test.XML");

                Console.WriteLine("Processing Rows Block - {0} ", currentPage);

                if (DBConnect.State != ConnectionState.Open)
                {
                    ConnectToDatabase();
                    Console.WriteLine("Open DB");
                }

                var bulk = new BulkOperation(DBConnect);

                bulk.DestinationTableName = TableName;
                bulk.BatchTimeout = 600;
                foreach (DataColumn DTTempCol in dtTemp.Columns)
                {
                    //if (DTTempCol.ColumnName != "updated")
                    bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                }

                List<string> PrimaryKeys = KeyId.Split(',').ToList();

                bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                Console.WriteLine("Merging Rows Block - {0} ", currentPage);

                bulk.BulkMerge(dtTemp);
                // Construct the DELETE query to remove rows not present in DTTempData
                string currentIds = string.Join(",", dtTemp.AsEnumerable().Select(row => $"'{row[KeyId]}'").Distinct());

                if (idsNotToDeleteBuilder.Length > 0)
                {
                    idsNotToDeleteBuilder.Append(",");
                }
                idsNotToDeleteBuilder.Append(currentIds);
                //Console.WriteLine(KeyId + " " + idsNotToDelete);

                AfterGet = DateTime.UtcNow;
                if(currentPage == totalPages)
                {
                    Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempData.Rows.Count , DTTempData.Rows.Count);
                }
                else
                {
                    Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);
                }
                currentPage++;
            }

            AfterGet = DateTime.UtcNow;
            Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);

            BeforeGet = DateTime.UtcNow;
            string deleteQuery = $"DELETE FROM {TableName} WHERE {KeyId} NOT IN ({idsNotToDeleteBuilder.ToString()})";
            //Console.WriteLine(deleteQuery);

            ExecuteSqlNonQuery(deleteQuery);

            AfterGet = DateTime.UtcNow;
            Console.WriteLine("Delete Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);

            // Return the connection to the pool by closing it
            // This doesn't actually close the connection but returns it to the pool
            if (DBConnect?.State == ConnectionState.Open)
            {
                _logger?.LogDebug("Returning database connection to the pool after bulk operation with deletion");
                DBConnect.Close();
            }
            Console.WriteLine("Connection returned to the pool");
            return true;
        }

#nullable enable
        public enum BulkOperationActionType
        {
            Save,
            Merge
        }

        private enum DynamicSQLActionType
        {
            Insert,
            Update
        }

        public void WriteSQLDataBulk(DataTable table)
        {
            WriteSQLDataBulk(BulkOperationActionType.Merge, table);
        }

        public void WriteSQLDataBulk(DataTable table, List<string> keys)
        {
            WriteSQLDataBulk(BulkOperationActionType.Merge, table, keys);
        }

        public void WriteSQLDataBulk(BulkOperationActionType actionType, DataTable table)
        {
            WriteSQLDataBulk(actionType, table, table.PrimaryKey.Select(x => x.ColumnName).ToList());
        }

        public void WriteSQLDataBulk(BulkOperationActionType actionType, DataTable table, List<string> keys)
        {
            // In case DB is Snowflake, redirect to custom built Bulk Insert/Update
            if (DBType == DatabaseType.Snowflake)
            {
                WriteSQLData(table, table.TableName.ToLower());
                return;
            }


            if (table.Rows.Count == 0)
                return;

            ConnectToDatabase();

            // Log connection state and pool status for diagnostics
            _logger?.LogDebug("Starting bulk operation for {TableName} with {RowCount} rows. Connection state: {ConnectionState}",
                table.TableName, table.Rows.Count, DBConnect.State);

            if (DBConnect is NpgsqlConnection npgsqlConn)
            {
                _logger?.LogDebug("PostgreSQL connection details - Host: {Host}, Database: {Database}, State: {State}",
                    npgsqlConn.Host, npgsqlConn.Database, npgsqlConn.State);
            }

            var timer = System.Diagnostics.Stopwatch.StartNew();

            using (var bulk = new BulkOperation(DBConnect))
            {
                bulk.CaseSensitive = CaseSensitiveType.Insensitive;
                bulk.DestinationTableName = table.TableName.ToLower();

                // Set appropriate timeout based on row count - smaller datasets should timeout faster
                if (table.Rows.Count <= 100)
                {
                    bulk.BatchTimeout = 60; // 1 minute for small datasets
                    _logger?.LogDebug("Using reduced timeout (60s) for small dataset of {RowCount} rows", table.Rows.Count);
                }
                else
                {
                    bulk.BatchTimeout = 600; // 10 minutes for larger datasets
                }

                bulk.BatchSize = Math.Min(1000, table.Rows.Count); // Don't use larger batch size than row count

                foreach (DataColumn column in table.Columns)
                {
                    bulk.ColumnMappings.Add(column.ColumnName);
                }
                bulk.ColumnPrimaryKeyNames = keys;
                if (_logger != null)
                    bulk.Log = s => _logger?.LogTrace(s);

                try
                {
                    switch (actionType)
                    {
                        case BulkOperationActionType.Save:
                            bulk.BulkSaveChanges(table);
                            break;
                        case BulkOperationActionType.Merge:
                            bulk.BulkMerge(table);
                            break;
                        default:
                            throw new NotImplementedException();
                    }
                }
                catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "23505") // Duplicate key violation
                {
                    // Log the error with details
                    string errorMessage = $"Duplicate key violation in table {table.TableName}: {pgEx.Message}";
                    _logger?.LogError(pgEx, errorMessage);
                    Console.WriteLine(errorMessage);

                    // Try to identify the problematic records
                    string constraintName = pgEx.ConstraintName ?? "unknown_constraint";

                    // For partitioned tables like detailedinteractiondata_p2024_05
                    if (constraintName.Contains("_pkey") && table.TableName.Contains("detailedinteractiondata"))
                    {
                        _logger?.LogWarning("Detected duplicate key in partitioned table {TableName} with constraint {Constraint}",
                            table.TableName, constraintName);

                        // Try a different approach - use BulkUpdate followed by BulkInsert instead of BulkMerge
                        try
                        {
                            _logger?.LogInformation("Attempting alternative approach with separate update and insert operations");

                            // First update existing records
                            bulk.BulkUpdate(table);

                            // Then try to insert new records, which might still fail if there are true duplicates
                            try
                            {
                                bulk.BulkInsert(table);
                            }
                            catch (Npgsql.PostgresException insertEx) when (insertEx.SqlState == "23505")
                            {
                                _logger?.LogWarning(insertEx, "Some records could not be inserted due to duplicate keys, but updates were processed");
                            }

                            Console.WriteLine("Alternative bulk operation completed in {0:N3} secs", timer.Elapsed.TotalSeconds);
                        }
                        catch (Exception altEx)
                        {
                            _logger?.LogError(altEx, "Alternative bulk operation approach also failed");
                            throw; // Re-throw if the alternative approach also fails
                        }
                    }
                    else
                    {
                        throw; // Re-throw for other types of constraint violations
                    }
                }
                catch (TimeoutException timeoutEx)
                {
                    _logger?.LogError(timeoutEx,
                        "DB:Write: Bulk upsert of {Rows} rows for {Table} TIMED OUT after {TimeTaken:N3}s. Connection state: {ConnectionState}",
                        table.Rows.Count,
                        table.TableName.ToLower(),
                        timer.Elapsed.TotalSeconds,
                        DBConnect?.State);

                    // Log additional PostgreSQL-specific diagnostics
                    if (DBConnect is NpgsqlConnection timeoutConn)
                    {
                        _logger?.LogError("PostgreSQL timeout details - Host: {Host}, Database: {Database}, Connection timeout: {ConnectionTimeout}s, Command timeout: {CommandTimeout}s",
                            timeoutConn.Host, timeoutConn.Database, timeoutConn.ConnectionTimeout, bulk.BatchTimeout);

                        // Check if connection is still alive
                        try
                        {
                            var testCmd = timeoutConn.CreateCommand();
                            testCmd.CommandText = "SELECT 1";
                            testCmd.CommandTimeout = 5;
                            testCmd.ExecuteScalar();
                            _logger?.LogWarning("Connection test successful - timeout appears to be at bulk operation level");
                        }
                        catch (Exception testEx)
                        {
                            _logger?.LogError(testEx, "Connection test failed - network or database connectivity issue detected");
                        }
                    }

                    throw;
                }
                catch (NpgsqlException npgsqlEx)
                {
                    _logger?.LogError(npgsqlEx,
                        "DB:Write: PostgreSQL error during bulk upsert of {Rows} rows for {Table} after {TimeTaken:N3}s. Error code: {ErrorCode}, SQL state: {SqlState}",
                        table.Rows.Count,
                        table.TableName.ToLower(),
                        timer.Elapsed.TotalSeconds,
                        npgsqlEx.ErrorCode,
                        npgsqlEx.SqlState);

                    // Log additional connection diagnostics for network/timeout issues
                    if (DBConnect is NpgsqlConnection errorConn)
                    {
                        _logger?.LogError("PostgreSQL connection state: {State}, Host: {Host}, Database: {Database}",
                            errorConn.State, errorConn.Host, errorConn.Database);
                    }

                    throw;
                }
                catch (Exception ex)
                {
                    if (_logger != null)
                        _logger.LogError(
                            ex,
                            "DB:Write: Bulk upsert of {Rows} rows for {Table} FAILED in {TimeTaken:N3}s. Connection state: {ConnectionState}",
                            table.Rows.Count,
                            table.TableName.ToLower(),
                            timer.Elapsed.TotalSeconds,
                            DBConnect?.State);
                    else
                        Console.WriteLine(
                            "Bulk upsert of {0} rows for {1} FAILED in {2:N3} secs",
                            table.Rows.Count,
                            table.TableName.ToLower(),
                            timer.Elapsed.TotalSeconds);
                    throw;
                }
            }

            if (_logger != null)
                _logger.LogInformation(
                    "DB:Write: Bulk upsert of {Rows} rows for {Table} completed in {TimeTaken:N3}s",
                    table.Rows.Count,
                    table.TableName.ToLower(),
                    timer.Elapsed.TotalSeconds);
            else
                Console.WriteLine(
                    "Bulk upsert of {0} rows for {1} completed in {2:N3} secs",
                    table.Rows.Count,
                    table.TableName.ToLower(),
                    timer.Elapsed.TotalSeconds);

            // Return the connection to the pool by closing it
            // This doesn't actually close the connection but returns it to the pool
            if (DBConnect?.State == ConnectionState.Open)
            {
                _logger?.LogDebug("Returning database connection to the pool after bulk operation");
                DBConnect.Close();
            }
        }

#nullable restore

        public Boolean WriteSQLDataSync(DataTable DTTempData, String TableName)
        {
            if (DBType == DatabaseType.Snowflake)
                return WriteSQLData(DTTempData, TableName);

            DateTime BeforeGet = DateTime.UtcNow;
            DateTime AfterGet = DateTime.UtcNow;

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            switch (DBType)
            {
                case DatabaseType.PostgreSQL:
                    TableName = PostgresSchema + "." + TableName.ToLower();
                    break;
                default:
                    break;
            }

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            if (DBConnect.State != ConnectionState.Open)
                ConnectToDatabase();

            int MaxRowsToSend = GetOptimalBatchSize("bulk");
            int currentPage = 1;

            int totalPages = 0;
            //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

            if (DTTempData.Rows.Count % MaxRowsToSend == 0)
            {
                Console.WriteLine("Reading Block of Data :Equal Division Pages is not adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
            }
            else
            {
                Console.WriteLine("Reading Block of Data :Not Equal Division Pages adding one");
                totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
            }

            while (currentPage <= totalPages)
            {

                DataTable dtTemp = DTTempData.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = DTTempData.TableName;
                //dtTemp.WriteXml(TableName + "Test.XML");

                Console.WriteLine("Processing Rows Block - {0} ", currentPage);
                var bulk = new BulkOperation(DBConnect);
                bulk.DestinationTableName = TableName;
                bulk.BatchTimeout = 180;
                foreach (DataColumn DTTempCol in dtTemp.Columns)
                {
                    //if (DTTempCol.ColumnName != "updated")
                    bulk.ColumnMappings.Add(DTTempCol.ColumnName);
                }

                List<string> PrimaryKeys = KeyId.Split(',').ToList();

                bulk.ColumnPrimaryKeyNames = PrimaryKeys;

                Console.WriteLine("Merging Rows Block - {0} ", currentPage);

                bulk.BulkSynchronize(dtTemp);

                AfterGet = DateTime.UtcNow;
                Console.WriteLine("SQL Syncing Current Page {1:N3} : Completed {0} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempData.Rows.Count);

                bulk.Dispose();

                currentPage++;
            }

            AfterGet = DateTime.UtcNow;
            Console.WriteLine("Bulk Syncing Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);

            // Return the connection to the pool by closing it
            // This doesn't actually close the connection but returns it to the pool
            if (DBConnect?.State == ConnectionState.Open)
            {
                _logger?.LogDebug("Returning database connection to the pool after SQL sync operation");
                DBConnect.Close();
            }
            Console.WriteLine("Connection returned to the pool");

            return true;
        }

        public bool WriteSQLData(DataTable DTTempData, String TableName)
        {
            string SearchString = String.Empty;

            DataSet DSFromDataBase = new DataSet();

            DataRow DRControl = ControlData.Select("tablename = '" + TableName + "'").FirstOrDefault();

            if (DRControl == null)
            {
                Console.WriteLine("The Table Definition Row for Table {0} is missing. \nNo Sync will happen until this is addressed", TableName);
                return false;
            }

            string KeyId = DRControl["keyfield"].ToString();

            if (DTTempData.Rows.Count == 0)
                return true;

            if (DRControl != null)
            {
                DateTime BeforeGet = DateTime.UtcNow;
                Console.WriteLine("Preparing to Write Data for the {0} Table", TableName);

                ConnectToDatabase();

                bool AddedSearchOptions = false;
                int MaxRowsToSend = GetOptimalBatchSize("merge");
                int currentPage = 1;

                int totalPages = 0;
                //int totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;

                if (DTTempData.Rows.Count % MaxRowsToSend == 0)
                {
                    Console.WriteLine("Reading Bock of Data :Equal Division Pages is not adding one");
                    totalPages = (DTTempData.Rows.Count / MaxRowsToSend);
                }
                else
                {
                    Console.WriteLine("Reading Bock of Data :Not Equal Division Pages adding one");
                    totalPages = (DTTempData.Rows.Count / MaxRowsToSend) + 1;
                }

                #region DBUpdateMethod
                // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
                switch (DBType)
                {
                    case DatabaseType.PostgreSQL:
                        TableName = PostgresSchema + "." + TableName.ToLower();
                        break;
                    default:
                        break;
                }

                DateTime AfterGet = DateTime.UtcNow;

                while (currentPage <= totalPages)
                {
                    DBSelectCommand = DBConnect.CreateCommand();
                    DBUpdateCommand = DBConnect.CreateCommand();
                    DBDeleteCommand = DBConnect.CreateCommand();

                    Console.WriteLine("Working On Batch Page : {0}", currentPage);
                    StringBuilder SelectString = new StringBuilder();
                    DataTable dtTemp = DTTempData.Select("", "", DataViewRowState.CurrentRows).Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                    dtTemp.TableName = DTTempData.TableName;

                    int CurrentRow = 0;
                    foreach (DataRow DRRow in dtTemp.Rows)
                    {
                        SelectString.Append(DRRow[KeyId] + ",");
                        AddedSearchOptions = true;
                        CurrentRow++;
                    }

                    if (AddedSearchOptions == true)
                    {
                        SelectString.Length = SelectString.Length - 1;
                    }
                    else
                        break;

                    Console.WriteLine("Filled Search String ");
                    // TODO: The way the SQL is generated here needs reworking.
                    switch (DBType)
                    {
                        case DatabaseType.MSSQL:
                            SearchString = "DECLARE @data NVARCHAR(MAX),  " +
                                            "@delimiter NVARCHAR(5) " +
                                            "SELECT @data = '" + SelectString.ToString() + "' " +
                                            "set @delimiter = ',' " +
                                            "DECLARE @textXML XML; " +
                                            "SELECT @textXML = CAST('<d>' + REPLACE(@data, @delimiter, '</d><d>') + '</d>' AS XML); " +
                                            "  " +
                                            "declare @temp table (num nvarchar(255)) " +
                                            "insert into @temp " +
                                            "SELECT T.split.value('.', 'nvarchar(max)') AS data " +
                                            "FROM @textXML.nodes('/d') T (split) " +
                                            " " +
                                            "Select * from " + TableName + "  where " + KeyId + " in(select num from @temp);";

                            DBSelectCommand.CommandText = @SearchString;

                            break;
                        case DatabaseType.MySQL:
                            MySqlCommand MYSQLcmd = new MySqlCommand
                            {
                                CommandType = CommandType.StoredProcedure,
                                CommandText = "FETCHROWS"
                            };

                            MYSQLcmd.Parameters.AddWithValue("@inputString", SelectString.ToString());
                            MYSQLcmd.Parameters["@inputString"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@dbName", DBConnect.Database);
                            MYSQLcmd.Parameters["@dbName"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@tableName", TableName);
                            MYSQLcmd.Parameters["@tableName"].Direction = ParameterDirection.Input;
                            MYSQLcmd.Parameters.AddWithValue("@keyField", KeyId);
                            MYSQLcmd.Parameters["@keyField"].Direction = ParameterDirection.Input;

                            DBSelectCommand.CommandText = MYSQLcmd.CommandText;
                            DBSelectCommand.CommandType = CommandType.StoredProcedure;

                            foreach (MySqlParameter DBParams in MYSQLcmd.Parameters)
                            {
                                DBSelectCommand.Parameters.Add(DBParams);
                            }

                            MYSQLcmd = null;
                            break;

                        case DatabaseType.PostgreSQL:
                            NpgsqlCommand PstSQLCmd = new NpgsqlCommand
                            {
                                //CommandType = CommandType.StoredProcedure,
                                CommandText = @"select * from " + TableName + " where " + KeyId + " in ('" + SelectString.ToString().Replace(",", "','") + "')"
                            };

                            DBSelectCommand.CommandText = PstSQLCmd.CommandText;
                            DBSelectCommand.CommandType = CommandType.Text;

                            break;

                        case DatabaseType.Snowflake:
                            DBSelectCommand.CommandText = @"select * from " + TableName + " where " + KeyId + " in ('" + SelectString.ToString().Replace(",", "','") + "')";
                            break;

                        default:
                            throw new NotImplementedException("Database type is not implemented");
                    }

                    Console.WriteLine("Getting Existing Data From DB");
                    DBSelectCommand.Connection = DBConnect;
                    DBSelectCommand.CommandTimeout = 0;
                    DBDataAdapter.SelectCommand = DBSelectCommand;
                    DBDataAdapter.Fill(DSFromDataBase);
                    Console.WriteLine("Got Existing Data From DB");

                    currentPage++;
                }

                DSFromDataBase.Tables[0].TableName = TableName;

                DBSelectCommand = null;

                _logger?.LogInformation("Table '{0}': Total rows from Genesys Cloud: {1}, Total rows from database: {2}",
                    TableName, DTTempData.Rows.Count, DSFromDataBase.Tables[0].Rows.Count);
                Console.WriteLine("\nTable '{0}': Total rows from Genesys Cloud: {1}", TableName, DTTempData.Rows.Count);
                Console.WriteLine("Table '{0}': Total rows from database: {1}", TableName, DSFromDataBase.Tables[0].Rows.Count);

                DTTempData.Columns.Remove("updated");
                DTTempData.AcceptChanges();
                DSFromDataBase.Tables[0].Columns.Remove("updated");
                DSFromDataBase.Tables[0].AcceptChanges();


                int AddedRows = 0;
                int ChangedRows = 0;
                // Identify new records
                var newRecords =
                    (from drNew in DTTempData.AsEnumerable()
                    join drOld in DSFromDataBase.Tables[0].AsEnumerable()
                    on drNew.Field<String>(KeyId) equals drOld.Field<String>(KeyId) into match
                    from drOld in match.DefaultIfEmpty()
                    where drOld == null
                    select drNew).ToList();

                // Identify updated records
                var potentialUpdatedRecords  =
                    (from drNew in DTTempData.AsEnumerable()
                    join drOld in DSFromDataBase.Tables[0].AsEnumerable()
                    on drNew.Field<String>(KeyId) equals drOld.Field<String>(KeyId)
                    where drOld != null && !DataRowComparer.Default.Equals(drNew, drOld)
                    select new { drNew, drOld }).ToList();

                var updatedRecords = new List<DataRow>();

                foreach (var record in potentialUpdatedRecords)
                {
                    var drNew = record.drNew;
                    var drOld = record.drOld;

                    bool isDifferent = false;

                    foreach (DataColumn column in DTTempData.Columns)
                    {
                        if (DSFromDataBase.Tables[0].Columns.Contains(column.ColumnName))
                        {
                            var newValue = drNew[column];
                            var oldValue = drOld[column.ColumnName];
                            bool valuesAreEqual;
                            if (newValue is DateTime newDateTime && oldValue is DateTime oldDateTime)
                            {
                                valuesAreEqual = newDateTime.ToString("yyyy-MM-dd HH:mm:ss") == oldDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            else if (newValue is string newString && oldValue is string oldString)
                            {
                                valuesAreEqual = string.Equals(newString?.Trim(), oldString?.Trim(), StringComparison.Ordinal);
                            }
                            else if (newValue is DBNull && oldValue is DBNull)
                            {
                                valuesAreEqual = true;
                            }
                            else if ((newValue is double || newValue is float || newValue is decimal) &&
                            (oldValue is double || oldValue is float || oldValue is decimal))
                            {
                                var oldDouble = Convert.ToDouble(oldValue);
                                string oldValueStr = oldDouble.ToString();
                                int precision = 0;
                                int decimalPlaceIndex = oldValueStr.IndexOf('.');
                                if (decimalPlaceIndex != -1)
                                {
                                    precision = oldValueStr.Length - decimalPlaceIndex - 1;
                                }
                                precision = Math.Min(precision, 15);
                                var newDouble = Convert.ToDouble(newValue);
                                double roundedNewValue = Math.Round(newDouble, precision, MidpointRounding.AwayFromZero);
                                double roundedOldValue = Math.Round(oldDouble, precision, MidpointRounding.AwayFromZero);

                                valuesAreEqual = roundedNewValue == roundedOldValue;
                            }
                            else if ((newValue == null || newValue == DBNull.Value || newValue.Equals(string.Empty)) &&
                                    (oldValue == null || oldValue == DBNull.Value || oldValue.Equals(string.Empty)))
                            {
                                valuesAreEqual = true;
                            }
                            else
                            {
                                valuesAreEqual = Equals(newValue, oldValue);
                            }

                            if (!valuesAreEqual)
                            {
                                isDifferent = true;
                                // Console.WriteLine($" Column: {column.ColumnName}, Old Value: {oldValue}, New Value: {newValue}");
                            }
                        }
                        else
                        {
                            // Console.WriteLine($"Column '{column.ColumnName}' does not exist in the database table.");
                        }
                    }

                    if (isDifferent)
                    {
                        updatedRecords.Add(drNew);
                    }
                }

				var addOrUpdateRecords = newRecords.Concat(updatedRecords).ToList();

                Console.WriteLine("\nTotal Rows to Add: {0}", newRecords.Count);
                Console.WriteLine("\nTotal Rows to Update: {0}", updatedRecords.Count);
                if (addOrUpdateRecords.Count > 0)
                {
                    DataTable DTChangedRows = addOrUpdateRecords.CopyToDataTable();
                    DTChangedRows.TableName = TableName;

                    DTChangedRows.Columns.Add("updated", typeof(DateTime));
                    DTChangedRows.AcceptChanges();

                    DataTable DTTempInsTable = DTChangedRows.Clone();
                    DataTable DTTempUpdTable = DTChangedRows.Clone();

                    foreach (DataRow DRChanges in DTChangedRows.Rows)
                    {
                        DataRow DRCheckRow = DSFromDataBase.Tables[0].Select(KeyId + " = '" + DRChanges[KeyId].ToString().Trim() + "'").FirstOrDefault();

                        if (DRCheckRow != null)
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetModified();
                            DTTempUpdTable.ImportRow(DRChanges);
                            ChangedRows++;
                            //Console.Write("*");
                            if (ChangedRows % 100 == 0)
                                Console.Write("*");
                        }
                        else
                        {
                            DRChanges["updated"] = DateTime.UtcNow;
                            DRChanges.AcceptChanges();
                            DRChanges.SetAdded();
                            DTTempInsTable.ImportRow(DRChanges);
                            AddedRows++;
                            //Console.Write("+");
                            if (AddedRows % 100 == 0)
                                Console.Write("+");
                        }
                    }

                    Console.WriteLine("\nAttempting Adapter Update");

                    var bulk = new BulkOperation(DBConnect)
                    {
                        DestinationTableName = TableName
                    };

                    foreach (DataColumn DCChangedRows in DTChangedRows.Columns)
                    {
                        if (DCChangedRows.ColumnName == KeyId)
                            bulk.ColumnMappings.Add(DCChangedRows.ColumnName, true);
                        else
                            bulk.ColumnMappings.Add(DCChangedRows.ColumnName);
                    }

                    if (ChangedRows > 0)
                    {
                        Console.WriteLine("Updating Rows - Count: {0} ", DTTempUpdTable.Rows.Count);

                        MaxRowsToSend = GetOptimalBatchSize("update");
                        currentPage = 1;

                        if (DTTempUpdTable.Rows.Count % MaxRowsToSend == 0)
                        {
                            Console.WriteLine("Writing Bock of Data :Equal Division Pages is not adding one");
                            totalPages = (DTTempUpdTable.Rows.Count / MaxRowsToSend);
                        }
                        else
                        {
                            Console.WriteLine("Writing Bock of Data :Not Equal Division Pages adding one");
                            totalPages = (DTTempUpdTable.Rows.Count / MaxRowsToSend) + 1;

                        }


                        while (currentPage <= totalPages)
                        {
                            DataTable dtTemp = DTTempUpdTable.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                            dtTemp.TableName = DTTempUpdTable.TableName;
                            //dtTemp.WriteXml(TableName + "Test.XML");
                            Console.WriteLine("Updating Rows Block - {0} ", currentPage);


                            if (DBType == DatabaseType.Snowflake)
                            {
                                //if (BulkUpdate(dtTemp, KeyId))
                                if (BulkMerge(dtTemp, KeyId))
                                {
                                    Console.WriteLine("Snowflake Records Updated.");
                                    AfterGet = DateTime.UtcNow;
                                    if(currentPage == totalPages)
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempUpdTable.Rows.Count , DTTempUpdTable.Rows.Count);
                                    }
                                    else
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempUpdTable.Rows.Count);
                                    }

                                }
                                else
                                {
                                    Console.WriteLine("Snowflake Records couldn't be updated.");
                                    return false;
                                }
                            }
                            else
                            {
                                bulk.BatchTimeout = 180;
                                bulk.BulkUpdate(dtTemp);
                            }

                            currentPage++;
                        }
                    }
                    else
                        Console.WriteLine("Updating Rows - No Rows to Update");

                    if (AddedRows > 0)
                    {
                        Console.WriteLine("Inserting Rows - Count: {0}", DTTempInsTable.Rows.Count);

                        //DTTempInsTable.WriteXml(TableName + "DataInsert.xml");

                        MaxRowsToSend = GetOptimalBatchSize("insert");
                        currentPage = 1;

                        if (DTTempInsTable.Rows.Count % MaxRowsToSend == 0)
                        {
                            Console.WriteLine("Equal Division Pages is not adding one");
                            totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend);
                        }
                        else
                        {
                            Console.WriteLine("Not Equal Division Pages adding one");
                            totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend) + 1;

                        }
                        //totalPages = (DTTempInsTable.Rows.Count / MaxRowsToSend) + 1;

                        while (currentPage <= totalPages)
                        {

                            DataTable dtTemp = DTTempInsTable.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                            dtTemp.TableName = DTTempInsTable.TableName;
                            //dtTemp.WriteXml(TableName + ".xml");
                            Console.WriteLine("Inserting Rows Block - {0} ", currentPage);
                            bulk.BatchTimeout = 180;

                            if (DBType == DatabaseType.Snowflake)
                            {
                                // Trigger as separate thread
                                if (BulkMerge(dtTemp, KeyId))
                                {
                                    Console.WriteLine("Records Inserted to Snowflake.");
                                    AfterGet = DateTime.UtcNow;
                                    if(currentPage == totalPages)
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, DTTempInsTable.Rows.Count , DTTempInsTable.Rows.Count);
                                    }
                                    else
                                    {
                                        Console.WriteLine("Bulk Upsert Current Page {1} : Completed {0:N3} secs. Records : {2} of {3} ", (AfterGet - BeforeGet).TotalSeconds, currentPage, currentPage * MaxRowsToSend, DTTempInsTable.Rows.Count);
                                    }
                                }
                                else
                                {
                                    Console.WriteLine("Records couldn't inserted to Snowflake.");
                                    return false;
                                }
                            }
                            else
                            {
                                #warning Snowflake is not supported by Z.BulkOperations
                                bulk.BulkInsert(dtTemp);
                            }
                            currentPage++;
                        }
                    }
                    else
                        Console.WriteLine("Inserting Rows - No Rows to Insert");

                    _logger?.LogInformation("Table '{0}': Added {1} rows, Updated {2} rows", TableName, AddedRows, ChangedRows);
                    Console.WriteLine("Table '{0}': Added {1} rows, Updated {2} rows", TableName, AddedRows, ChangedRows);
                }
                else
                    Console.WriteLine("No Updates Required");
                #endregion

                AfterGet = DateTime.UtcNow;
                Console.WriteLine("Bulk Upsert Completed {0:N3} secs", (AfterGet - BeforeGet).TotalSeconds);
            }

            if (DBConnect.State == ConnectionState.Open)
                CloseConnectionToDatabase();

            DTTempData.Columns.Add("updated", typeof(DateTime));
            DTTempData.AcceptChanges();

            return true;
        }

        private bool BulkInsert(DataTable dtTemp)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();
            int rowsCount = 0;
            int colsCount = 0;

            #region Prepare SQL Script to Insert Bulk Data

            foreach (DataRow dr in dtTemp.Rows)
            {
                if (rowsCount == 0)
                {
                    sb.AppendLine($"INSERT INTO {dtTemp.TableName} (");
                    foreach (DataColumn dc in dtTemp.Columns)
                    {
                        if (colsCount > 0) sb.Append(", ");
                        sb.AppendLine(dc.ColumnName);
                        colsCount++;
                    }
                    sb.AppendLine(") ");
                    sb.AppendLine("VALUES ");
                }
                else
                {
                    sb.Append(", ");
                }

                sb.AppendLine("(");

                colsCount = 0;
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (colsCount > 0) sb.Append(", ");

                    sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Insert));
                    colsCount++;
                }

                sb.AppendLine(")");

                rowsCount++;
            }

            #endregion

            // Insert into Database
            if (sb.ToString().Length > 0)
                try
                {
                    Successful = ExecuteSQLQuery(sb.ToString());
                }
                catch (System.Exception ex)
                {
                    Console.WriteLine(sb.ToString());
                    Console.WriteLine(ex.Message);
                    //throw;
                }

            return Successful;
        }

        private bool BulkUpdate(DataTable dtTemp, string KeyId)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();
            int rowsCount = 0;
            int colsCount = 0;

            #region Prepare SQL Script to Update Bulk Data

            foreach (DataRow dr in dtTemp.Rows)
            {
                colsCount = 0;
                sb.AppendLine($"-- Update record {rowsCount + 1}  --");
                sb.AppendLine($"UPDATE  {dtTemp.TableName} ");
                sb.AppendLine($"SET ");

                //Set Value
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (colsCount > 0) sb.Append(", ");

                    sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Update));

                    colsCount++;
                }
                sb.AppendLine(" WHERE ");
                // Where Clause
                colsCount = 0;
                foreach (DataColumn dc in dtTemp.Columns)
                {
                    if (KeyId.ToLower() == dc.ColumnName.ToLower())
                    {
                        sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Update));
                    }
                    colsCount++;
                }

                sb.AppendLine(";");
                sb.AppendLine(" -- **********  ");
                rowsCount++;

            }

            #endregion

            // Update Records
            if (sb.ToString().Length > 0)
            {
                Successful = ExecuteSQLQuery(sb.ToString());
            }


            return Successful;
        }

        private bool BulkMerge(DataTable dtTemp, string KeyId)
        {
            // For Snowflake, use optimized batch processing instead of massive MERGE statements
            if (DBType == DatabaseType.Snowflake)
            {
                return BulkMergeSnowflakeOptimized(dtTemp, KeyId);
            }

            bool Successful = false;
            StringBuilder sb = new StringBuilder();
            var totalTimer = System.Diagnostics.Stopwatch.StartNew();
            var sqlGenerationTimer = System.Diagnostics.Stopwatch.StartNew();

            int rowsCount = 0;
            int colsCount = 0;

            try
            {
                string columnNames = GetSource(dtTemp);
                string updateClause = GetUpdateClause(dtTemp, KeyId);
                string insertClause = GetInsertClause(dtTemp);

                sb.AppendLine($"MERGE INTO {dtTemp.TableName} AS target");
                sb.AppendLine($"USING (");
                sb.AppendLine($"    VALUES ");

                foreach (DataRow dr in dtTemp.Rows)
                {
                    if (rowsCount > 0) sb.Append(",");

                    sb.AppendLine("(");
                    colsCount = 0;
                    foreach (DataColumn dc in dtTemp.Columns)
                    {
                        if (dc.ColumnName.ToLower().Equals("null")) continue;
                        if (colsCount > 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Insert));
                        colsCount++;
                    }
                    sb.Append(")");
                    rowsCount++;
                }

                sb.AppendLine($" AS source ({columnNames})");

                sb.AppendLine($" )"); // End Using

                sb.AppendLine($"ON target.{KeyId} = source.{KeyId}");
                sb.AppendLine($"WHEN MATCHED THEN");
                sb.AppendLine($"UPDATE SET");
                sb.AppendLine($"        {updateClause}");
                sb.AppendLine($"WHEN NOT MATCHED THEN");
                sb.AppendLine($"INSERT ({columnNames})");
                sb.AppendLine($"VALUES ({insertClause})");

                sqlGenerationTimer.Stop();

                // Log SQL generation performance for Snowflake
                if (DBType == DatabaseType.Snowflake)
                {
                    string sqlStatement = sb.ToString();
                    _logger?.LogDebug("Snowflake BulkMerge: SQL generation completed in {GenerationTime:F3}s, statement size: {StatementSize} characters",
                        sqlGenerationTimer.Elapsed.TotalSeconds, sqlStatement.Length);

                    // Log memory usage estimate
                    long estimatedMemoryUsage = sqlStatement.Length * 2; // Rough estimate for Unicode string
                    _logger?.LogDebug("Snowflake BulkMerge: Estimated memory usage: {MemoryUsage:N0} bytes", estimatedMemoryUsage);
                }

                // Update Records
                if (sb.ToString().Length > 0)
                {
                    var executionTimer = System.Diagnostics.Stopwatch.StartNew();
                    Successful = ExecuteSQLQuery(sb.ToString());
                    executionTimer.Stop();

                    if (DBType == DatabaseType.Snowflake)
                    {
                        _logger?.LogInformation("Snowflake BulkMerge: Execution completed in {ExecutionTime:F3}s for {RowCount} rows, total time: {TotalTime:F3}s",
                            executionTimer.Elapsed.TotalSeconds, dtTemp.Rows.Count, totalTimer.Elapsed.TotalSeconds);
                    }
                }
            }
            catch (Exception ex)
            {
                totalTimer.Stop();
                if (DBType == DatabaseType.Snowflake)
                {
                    _logger?.LogError(ex, "Snowflake BulkMerge: Operation failed after {TotalTime:F3}s for {RowCount} rows in table {TableName}. SQL generation time: {GenerationTime:F3}s",
                        totalTimer.Elapsed.TotalSeconds, dtTemp.Rows.Count, dtTemp.TableName, sqlGenerationTimer.Elapsed.TotalSeconds);
                }
                else
                {
                    Console.WriteLine("Writing DB Write Error");
                }
                throw;
            }
            finally
            {
                totalTimer.Stop();
                if (DBType == DatabaseType.Snowflake && Successful)
                {
                    _logger?.LogDebug("Snowflake BulkMerge: Operation completed successfully in {TotalTime:F3}s for {RowCount} rows",
                        totalTimer.Elapsed.TotalSeconds, dtTemp.Rows.Count);
                }
            }
            return Successful;
        }

        /// <summary>
        /// Optimized BulkMerge implementation specifically for Snowflake
        /// Uses smaller batches and more efficient SQL generation to avoid massive MERGE statements
        /// </summary>
        private bool BulkMergeSnowflakeOptimized(DataTable dtTemp, string KeyId)
        {
            var totalTimer = System.Diagnostics.Stopwatch.StartNew();
            bool overallSuccess = true;
            int processedRows = 0;

            _logger?.LogInformation("Snowflake BulkMerge: Starting optimized operation for {RowCount} rows in table {TableName}",
                dtTemp.Rows.Count, dtTemp.TableName);

            try
            {
                // Use very small batches for Snowflake to avoid SQL statement size issues
                int snowflakeBatchSize = 100; // Much smaller than the default
                int totalBatches = (int)Math.Ceiling((double)dtTemp.Rows.Count / snowflakeBatchSize);

                _logger?.LogDebug("Snowflake BulkMerge: Processing {TotalRows} rows in {BatchCount} batches of {BatchSize} rows each",
                    dtTemp.Rows.Count, totalBatches, snowflakeBatchSize);

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
                {
                    var batchTimer = System.Diagnostics.Stopwatch.StartNew();

                    // Create a small batch
                    var batchRows = dtTemp.Rows.Cast<DataRow>()
                        .Skip(batchIndex * snowflakeBatchSize)
                        .Take(snowflakeBatchSize)
                        .ToArray();

                    if (batchRows.Length == 0) continue;

                    // Create a temporary table for this batch
                    DataTable batchTable = dtTemp.Clone();
                    foreach (DataRow row in batchRows)
                    {
                        batchTable.ImportRow(row);
                    }
                    batchTable.TableName = dtTemp.TableName;

                    // Process this small batch with the original method
                    bool batchSuccess = BulkMergeSnowflakeSmallBatch(batchTable, KeyId);

                    if (batchSuccess)
                    {
                        processedRows += batchRows.Length;
                        batchTimer.Stop();

                        if ((batchIndex + 1) % 10 == 0 || batchIndex == totalBatches - 1)
                        {
                            _logger?.LogDebug("Snowflake BulkMerge: Completed batch {BatchIndex}/{TotalBatches} in {BatchTime:F3}s, processed {ProcessedRows}/{TotalRows} rows",
                                batchIndex + 1, totalBatches, batchTimer.Elapsed.TotalSeconds, processedRows, dtTemp.Rows.Count);
                        }
                    }
                    else
                    {
                        _logger?.LogError("Snowflake BulkMerge: Batch {BatchIndex}/{TotalBatches} failed", batchIndex + 1, totalBatches);
                        overallSuccess = false;
                        break;
                    }
                }

                totalTimer.Stop();

                if (overallSuccess)
                {
                    _logger?.LogInformation("Snowflake BulkMerge: Successfully processed {ProcessedRows} rows in {TotalTime:F3}s ({RowsPerSecond:F1} rows/sec)",
                        processedRows, totalTimer.Elapsed.TotalSeconds, processedRows / totalTimer.Elapsed.TotalSeconds);
                }

                return overallSuccess;
            }
            catch (Exception ex)
            {
                totalTimer.Stop();
                _logger?.LogError(ex, "Snowflake BulkMerge: Optimized operation failed after {TotalTime:F3}s, processed {ProcessedRows}/{TotalRows} rows",
                    totalTimer.Elapsed.TotalSeconds, processedRows, dtTemp.Rows.Count);
                return false;
            }
        }

        /// <summary>
        /// Processes a small batch for Snowflake using the original MERGE logic
        /// </summary>
        private bool BulkMergeSnowflakeSmallBatch(DataTable dtTemp, string KeyId)
        {
            bool Successful = false;
            StringBuilder sb = new StringBuilder();

            int rowsCount = 0;
            int colsCount = 0;
            try
            {
                string columnNames = GetSource(dtTemp);
                string updateClause = GetUpdateClause(dtTemp, KeyId);
                string insertClause = GetInsertClause(dtTemp);

                sb.AppendLine($"MERGE INTO {dtTemp.TableName} AS target");
                sb.AppendLine($"USING (");
                sb.AppendLine($"    VALUES ");

                foreach (DataRow dr in dtTemp.Rows)
                {
                    if (rowsCount > 0) sb.Append(",");

                    sb.AppendLine("(");
                    colsCount = 0;
                    foreach (DataColumn dc in dtTemp.Columns)
                    {
                        if (dc.ColumnName.ToLower().Equals("null")) continue;
                        if (colsCount > 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(GetTypedValueFromDataColumn(dr, dc, DynamicSQLActionType.Insert));
                        colsCount++;
                    }
                    sb.Append(")");
                    rowsCount++;
                }

                sb.AppendLine($" AS source ({columnNames})");
                sb.AppendLine($" )"); // End Using
                sb.AppendLine($"ON target.{KeyId} = source.{KeyId}");
                sb.AppendLine($"WHEN MATCHED THEN");
                sb.AppendLine($"UPDATE SET");
                sb.AppendLine($"        {updateClause}");
                sb.AppendLine($"WHEN NOT MATCHED THEN");
                sb.AppendLine($"INSERT ({columnNames})");
                sb.AppendLine($"VALUES ({insertClause})");

                // Execute the small batch
                if (sb.ToString().Length > 0)
                {
                    Successful = ExecuteSQLQuery(sb.ToString());
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Snowflake BulkMerge: Small batch failed for {RowCount} rows", dtTemp.Rows.Count);
                throw;
            }
            return Successful;
        }

        private string GetSource(System.Data.DataTable dtTemp)
        {
            StringBuilder sb = new StringBuilder();

            int colsCount = 0;


            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().ToString().Equals("null")) continue;
                if (colsCount > 0) sb.Append(",");


                if (!dc.ColumnName.Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                    sb.Append($"{dc.ColumnName}");
                else
                    sb.Append($"{dc.ColumnName}");


                colsCount++;
            }
            return sb.ToString();
        }

        private string GetUpdateClause(System.Data.DataTable dtTemp, string keyId)
        {
            StringBuilder sb = new StringBuilder();
            int colsCount = 0;

            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().Equals("null")) continue;

                if (sb.Length > 0) sb.Append(",");
                if (!dc.ColumnName.Equals(keyId, StringComparison.InvariantCultureIgnoreCase))
                    if (!dc.ColumnName.ToLower().Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                        sb.Append($"{dc.ColumnName} = source.{dc.ColumnName}");
                    else
                        sb.Append($"{dc.ColumnName} = source.{dc.ColumnName}");

                colsCount++;
            }
            return sb.ToString();
        }

        private string GetInsertClause(System.Data.DataTable dtTemp)
        {
            StringBuilder sbInsert = new StringBuilder();

            int colsCount = 0;

            foreach (DataColumn dc in dtTemp.Columns)
            {
                if (dc.ColumnName.ToLower().ToString().Equals("null")) continue;

                if (colsCount > 0) sbInsert.Append(",");
                if (!dc.ColumnName.Equals("updated", StringComparison.InvariantCultureIgnoreCase))
                    sbInsert.Append($"source.{dc.ColumnName}");
                else
                    sbInsert.Append($"source.{dc.ColumnName}");

                colsCount++;
            }
            return sbInsert.ToString();
        }


        private string GetTypedValueFromDataColumn(DataRow dr, DataColumn dc, DynamicSQLActionType actionType)
        {
            try
            {
                string typedValue = string.Empty;
                if (dc.DataType == typeof(string))
                {
                    typedValue = ($"'{dr[dc.ColumnName].ToString().Replace("'", "''")}'");
                    typedValue = typedValue.Replace("\\", "\\\\\\\\");

                }
                else if (dc.DataType == typeof(int) || dc.DataType == typeof(decimal) || dc.DataType == typeof(bool))
                {
                    typedValue = ($"{dr[dc.ColumnName]}");
                }
                else if (dc.DataType == typeof(DateTime))
                {
                    if (dr[dc.ColumnName] != DBNull.Value)
                    {
                        DateTime dt = Convert.ToDateTime(dr[dc.ColumnName]);
                        typedValue = $"'{dt.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                }
                else
                {
                    typedValue = $"'{dr[dc.ColumnName].ToString()}'";
                }

                if (string.IsNullOrEmpty(typedValue) || typedValue == "''")
                    typedValue = "null";



                if (actionType == DynamicSQLActionType.Update)
                {
                    if (dc.ColumnName != "updated")
                        return $"\"{dc.ColumnName.ToUpper()}\" = {typedValue}";
                    else
                        return $"{dc.ColumnName} = {typedValue}";

                }

                return typedValue;
            }
            catch (System.Exception ex)
            {

                throw;
            }
        }

        public bool WriteDynamicSQLData(DataTable DTTempData, String TableName)
        {
            bool Successful = false;
            Console.WriteLine("DBUtils:Checking Columns for Dynamic Data Storage\nTable Name {0} \nActual Tab Name {1} Total Rows {2}\n", TableName, DTTempData.TableName, DTTempData.Rows.Count);
            try
            {
                string SQLStatement = string.Empty;

                switch (DBType)
                {
                    case DatabaseType.MSSQL:
                        SQLStatement = "Select TOP (0) * From " + TableName;
                        break;
                    case DatabaseType.MySQL:
                    case DatabaseType.PostgreSQL:
                    case DatabaseType.Snowflake:
                        SQLStatement = "Select * From " + TableName + " limit 0";
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }

                DataTable DTCompare = GetSQLTableData(SQLStatement, TableName).Clone();
                DataColumnCollection columns = DTCompare.Columns;

                foreach (DataColumn DCTemp in DTTempData.Columns)
                {
                    //Console.WriteLine("Comparing Column Names {0} ", DCTemp.ColumnName);
                    Console.Write("CC:");
                    if (columns.Contains(DCTemp.ColumnName) == false)
                    {
                        if (DCTemp.MaxLength > 0)
                        {
                            Successful = AddColumnToDB(TableName, DCTemp.ColumnName, DCTemp.DataType.ToString(), DCTemp.MaxLength);
                        }
                        else
                        {
                            Successful = AddColumnToDB(TableName, DCTemp.ColumnName, DCTemp.DataType.ToString());
                        }
                    }
                }

                Console.WriteLine("\n");

                Successful = WriteSQLDataBulk(DTTempData, TableName);
            }
            catch (System.Exception ex)
            {
                Console.WriteLine("Dynamic Writing DB Write Error");
                throw;
            }

            return Successful;
        }

        public bool MYSQLScript(string SQLCommand)
        {
            ConnectToDatabase();

            if (DBConnect.State == ConnectionState.Open)
            {
                MySqlScript MySqlscript = new MySqlScript((MySqlConnection)DBConnect, SQLCommand);
                MySqlscript.Delimiter = "$$";
                MySqlscript.Execute();
            }
            CloseConnectionToDatabase();

            return true;
        }

#nullable enable
        /// <summary>
        /// Execute a non-query SQL statement
        /// </summary>
        /// <param name="sql">the SQL text to execute.</param>
        /// <param name="commandTimeout">If specified, overrides the default command timeout, in seconds.</param>
        /// <param name="isInstallMode">If true, forces cache bypass regardless of query type (used during installation)</param>
        public int ExecuteSqlNonQuery(string sql, int commandTimeout = -1, bool isInstallMode = false)
        {
            int rowsAffected = 0;
            var timer = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Ensure we have an open connection
                // If DBConnect is null, ConnectToDatabase will initialize it
                ConnectToDatabase();

                // After ConnectToDatabase, we should have an open connection
                // If not, there's a serious issue that needs to be addressed
                if (DBConnect == null)
                {
                    throw new InvalidOperationException("Database connection is null after connection attempt");
                }

                if (DBConnect.State != ConnectionState.Open)
                {
                    throw new InvalidOperationException("Database connection is not open after connection attempt");
                }

                // Log the operation type
                if (isInstallMode)
                {
                    _logger?.LogDebug("DB: Executing SQL operation in installation mode");
                }
                else
                {
                    _logger?.LogDebug("DB: Executing non-query SQL operation");
                }

                using (DBSelectCommand = DBConnect.CreateCommand())
                {
                    DBSelectCommand.CommandText = sql;
                    if (commandTimeout >= 0)
                        DBSelectCommand.CommandTimeout = commandTimeout;
                    else
                        DBSelectCommand.CommandTimeout = 180; // Set a reasonable default timeout

                    if (DBType == DatabaseType.Snowflake)
                    {
                        // Set statement count
                        var stmtCountParam = DBSelectCommand.CreateParameter();
                        stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                        stmtCountParam.DbType = DbType.Int16;
                        stmtCountParam.Value = 0;
                        DBSelectCommand.Parameters.Add(stmtCountParam);
                    }

                    try
                    {
                        rowsAffected = DBSelectCommand.ExecuteNonQuery();
                        _logger?.LogDebug("SQL non-query executed successfully in {TimeTaken:N3}s, rows affected: {RowsAffected}",
                            timer.Elapsed.TotalSeconds, rowsAffected);
                    }
                    catch (Exception ex) when (ex.InnerException is TimeoutException)
                    {
                        _logger?.LogError(
                            ex,
                            "SQL command timed out, executed for {TimeTaken:N3} secs, " +
                            "command timeout is {CommandTimeout:N3} secs.",
                            timer.Elapsed.TotalSeconds,
                            DBSelectCommand.CommandTimeout);

                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error executing SQL non-query: {SQL}", sql);
                throw;
            }

            // Keep the connection open for connection pooling
            // The connection will be managed by the connection pool
            return rowsAffected;
        }

        /// <summary>
        /// Executes a SQL query and returns the first column of the first row in the result set.
        /// </summary>
        /// <param name="sql">The SQL query to execute</param>
        /// <param name="commandTimeout">If specified, overrides the default command timeout, in seconds.</param>
        /// <returns>The first column of the first row in the result set, or null if no rows</returns>
        public object? ExecuteScalar(string sql, int commandTimeout = -1)
        {
            if (string.IsNullOrEmpty(sql))
                return null;

            if (DBConnect.State != ConnectionState.Open)
                ConnectToDatabase();

            if (DBConnect.State != ConnectionState.Open)
                throw new ApplicationException("Unable to connect to database");

            using (DBSelectCommand = DBConnect.CreateCommand())
            {
                DBSelectCommand.CommandText = sql;
                if (commandTimeout >= 0)
                    DBSelectCommand.CommandTimeout = commandTimeout;

                if (DBType == DatabaseType.Snowflake)
                {
                    // Set statement count
                    var stmtCountParam = DBSelectCommand.CreateParameter();
                    stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                    stmtCountParam.DbType = DbType.Int16;
                    stmtCountParam.Value = 0;
                    DBSelectCommand.Parameters.Add(stmtCountParam);
                }

                var timer = System.Diagnostics.Stopwatch.StartNew();
                try
                {
                    return DBSelectCommand.ExecuteScalar();
                }
                catch (Exception ex) when (ex.InnerException is TimeoutException)
                {
                    _logger?.LogError(
                        ex,
                        "SQL scalar query timed out, executed for {TimeTaken:N3} secs, " +
                        "command timeout is {CommandTimeout:N3} secs.",
                        timer.Elapsed.TotalSeconds,
                        DBSelectCommand.CommandTimeout);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(
                        ex,
                        "Error executing SQL scalar query: {SQL}",
                        sql);
                    throw;
                }
                finally
                {
                    CloseConnectionToDatabase();
                }
            }
        }

        public bool ExecuteSQLQuery(string SQLCommand)
        {
            var timer = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                // Ensure we have an open connection
                // If DBConnect is null, ConnectToDatabase will initialize it
                ConnectToDatabase();

                // After ConnectToDatabase, we should have an open connection
                if (DBConnect == null)
                {
                    _logger?.LogError("Database connection is null after connection attempt");
                    return false;
                }

                if (DBConnect.State != ConnectionState.Open)
                {
                    _logger?.LogError("Database connection is not open after connection attempt");
                    return false;
                }

                using (DBSelectCommand = DBConnect.CreateCommand())
                {
                    DBSelectCommand.CommandText = SQLCommand;
                    DBSelectCommand.CommandTimeout = 180; // Set a reasonable timeout

                    if (DBType == DatabaseType.Snowflake)
                    {
                        // Set statement count
                        var stmtCountParam = DBSelectCommand.CreateParameter();
                        stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                        stmtCountParam.DbType = DbType.Int16;
                        stmtCountParam.Value = 0;
                        DBSelectCommand.Parameters.Add(stmtCountParam);
                    }

                    try
                    {
                        DBSelectCommand.ExecuteNonQuery();
                        _logger?.LogDebug("SQL query executed successfully in {TimeTaken:N3}s", timer.Elapsed.TotalSeconds);
                        return true;
                    }
                    catch (Exception ex) when (ex.InnerException is TimeoutException)
                    {
                        _logger?.LogError(
                            ex,
                            "SQL command timed out, executed for {TimeTaken:N3} secs, command timeout is {CommandTimeout:N3} secs.",
                            timer.Elapsed.TotalSeconds,
                            DBSelectCommand.CommandTimeout);
                        return false;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error executing SQL query: {SQL}", SQLCommand);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in ExecuteSQLQuery: {SQL}", SQLCommand);
                return false;
            }
            // Keep the connection open for connection pooling
            // The connection will be managed by the connection pool
        }
#nullable restore

        // Constants for column types
        private const string TYPE_STRING = "system.string";
        private const string TYPE_INT32 = "system.int32";
        private const string TYPE_INT16 = "system.int16";
        private const string TYPE_DECIMAL = "system.decimal";
        private const string TYPE_BOOLEAN = "system.boolean";
        private const string TYPE_DATETIME = "system.datetime";
        private const string TYPE_TEXT = "system.text";
        private const string ALTER_TABLE_PREFIX = "ALTER TABLE ";
        private const string PASSWORD_PARAM = "Password";
        private const string CONNECTION_STRING_FORMAT = "Using connection string {0}";

        public Boolean AddColumnToDB(string Tablename, string ColumnName, string ColumnType, int ColumnSize = 0)
        {

            string ColType = string.Empty;

            if (ColumnSize == 0)
            {
                switch (DBType)
                {
                    case DatabaseType.MSSQL:
                        ColumnSize = 200;
                        break;
                    case DatabaseType.MySQL:
                        ColumnSize = 50;
                        break;
                    case DatabaseType.PostgreSQL:
                    case DatabaseType.Snowflake:
                        ColumnSize = 100;
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }
            }

            Console.WriteLine("Adding Col: {0} to Table:{1} Type : {2}", ColumnName, Tablename, ColumnType);

            string AddColumn = String.Empty;
            string columnTypeLower = ColumnType.ToLower();

            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    switch (columnTypeLower)
                    {
                        case TYPE_STRING:
                            ColType = "[nvarchar](" + ColumnSize + ")";
                            break;
                        case TYPE_INT32:
                        case TYPE_INT16:
                            ColType = "[int]";
                            break;
                        case TYPE_DECIMAL:
                            ColType = "[decimal](20, 2)";
                            break;
                        case TYPE_BOOLEAN:
                            ColType = "[bit]";
                            break;
                        case TYPE_DATETIME:
                            ColType = "[datetime]";
                            break;
                        case TYPE_TEXT:
                            ColType = "[nvarchar](max)";
                            break;
                    }
                    AddColumn = ALTER_TABLE_PREFIX + Tablename + " " +
                                "ADD [" + ColumnName + "] " + ColType + " NULL;";
                    break;

                case DatabaseType.MySQL:
                    switch (columnTypeLower)
                    {
                        case TYPE_STRING:
                            ColType = "nvarchar (" + ColumnSize + ")";
                            break;
                        case TYPE_INT32:
                            ColType = "int";
                            break;
                        case TYPE_DECIMAL:
                            ColType = "decimal (20, 2)";
                            break;
                        case TYPE_BOOLEAN:
                            ColType = "tinyint";
                            break;
                        case TYPE_DATETIME:
                            ColType = "datetime ";
                            break;
                        case TYPE_TEXT:
                            ColType = "mediumtext ";
                            break;
                    }


                    AddColumn = ALTER_TABLE_PREFIX + Tablename + " " +
                                "ADD `" + ColumnName + "` " + ColType + " NULL;";
                    break;


                case DatabaseType.PostgreSQL:
                    switch (columnTypeLower)
                    {
                        case TYPE_STRING:
                            ColType = "character varying(" + ColumnSize + ")";
                            break;
                        case TYPE_INT32:
                            ColType = "integer";
                            break;
                        case TYPE_DECIMAL:
                            ColType = "numeric(20, 2)";
                            break;
                        case TYPE_BOOLEAN:
                            ColType = "bit";
                            break;
                        case TYPE_DATETIME:
                            ColType = "timestamp without time zone";
                            break;
                        case TYPE_TEXT:
                            ColType = "text ";
                            break;
                    }


                    AddColumn = ALTER_TABLE_PREFIX + Tablename.ToLower() + " " +
                                "ADD COLUMN \"" + ColumnName.ToLower() + "\" " + ColType + ";";
                    break;

                case DatabaseType.Snowflake:
                    switch (columnTypeLower)
                    {
                        case TYPE_STRING:
                            ColType = "varchar (" + ColumnSize + ")";
                            break;
                        case TYPE_INT32:
                        case TYPE_INT16:
                            ColType = "number (38,0)";
                            break;
                        case TYPE_DECIMAL:
                            ColType = "number (20, 2)";
                            break;
                        case TYPE_BOOLEAN:
                            ColType = "boolean";
                            break;
                        case TYPE_DATETIME:
                            ColType = "TIMESTAMP_LTZ";
                            break;
                        case TYPE_TEXT:
                            ColType = "nvarchar";
                            break;
                    }

                    AddColumn = ALTER_TABLE_PREFIX + Tablename.ToLower() + " " +
                               "ADD COLUMN IF NOT exists " + ColumnName.ToLower() + " " + ColType + ";";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            ExecuteSqlNonQuery(AddColumn);

            // Log the result of the operation
            _logger?.LogDebug("Column {ColumnName} added to table {TableName}", ColumnName, Tablename);
            return true;
        }

        /// <summary>
        /// Execute a non-query SQL statement specifically for installation operations.
        /// </summary>
        /// <param name="sql">the SQL text to execute.</param>
        /// <param name="commandTimeout">If specified, overrides the default command timeout, in seconds.</param>
        public int ExecuteSqlNonQueryForInstall(string sql, int commandTimeout = -1)
        {
            int rowsAffected = 0;

            // Log that we're executing an installation operation
            _logger?.LogDebug("DB: Executing SQL operation for installation");

            // Ensure we have an open connection
            // If DBConnect is null, ConnectToDatabase will initialize it
            ConnectToDatabase();

            // After ConnectToDatabase, we should have an open connection
            // If not, there's a serious issue that needs to be addressed
            if (DBConnect == null)
            {
                throw new InvalidOperationException("Database connection is null after connection attempt");
            }

            if (DBConnect.State != ConnectionState.Open)
                throw new InvalidOperationException("Database connection is not open after connection attempt");

            using (DBSelectCommand = DBConnect.CreateCommand())
            {
                DBSelectCommand.CommandText = sql;
                if (commandTimeout >= 0)
                    DBSelectCommand.CommandTimeout = commandTimeout;
                else
                    DBSelectCommand.CommandTimeout = 180; // Set a reasonable default timeout for installation

                if (DBType == DatabaseType.Snowflake)
                {
                    // Set statement count
                    var stmtCountParam = DBSelectCommand.CreateParameter();
                    stmtCountParam.ParameterName = "MULTI_STATEMENT_COUNT";
                    stmtCountParam.DbType = DbType.Int16;
                    stmtCountParam.Value = 0;
                    DBSelectCommand.Parameters.Add(stmtCountParam);
                }

                var timer = System.Diagnostics.Stopwatch.StartNew();
                try
                {
                    rowsAffected = DBSelectCommand.ExecuteNonQuery();
                    _logger?.LogDebug("INSTALL: SQL non-query executed successfully in {TimeTaken:N3}s, rows affected: {RowsAffected}",
                        timer.Elapsed.TotalSeconds, rowsAffected);
                }
                catch (Exception ex) when (ex.InnerException is TimeoutException)
                {
                    _logger?.LogError(
                        ex,
                        "INSTALL: SQL command timed out, executed for {TimeTaken:N3} secs, " +
                        "command timeout is {CommandTimeout:N3} secs.",
                        timer.Elapsed.TotalSeconds,
                        DBSelectCommand.CommandTimeout);

                    throw;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "INSTALL: Error executing SQL non-query: {SQL}", sql);
                    throw;
                }
            }
            _logger?.LogDebug("Explicitly closing database connection at end of job");
            CloseConnectionToDatabase();
            return rowsAffected;
        }


#nullable enable
        public Database ParseConnectionString(
            Database databaseOptions,
            string connectionString)
        {
            switch (databaseOptions.Type)
            {
                case DatabaseType.MSSQL:
                    var mssqlBuilder = new SqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(mssqlBuilder.DataSource))
                        databaseOptions.Address = mssqlBuilder.DataSource;
                    if (!string.IsNullOrEmpty(mssqlBuilder.InitialCatalog))
                        databaseOptions.Name = mssqlBuilder.InitialCatalog;
                    if (!string.IsNullOrEmpty(mssqlBuilder.UserID) && !string.IsNullOrEmpty(mssqlBuilder.Password))
                    {
                        databaseOptions.User = mssqlBuilder.UserID;
                        databaseOptions.Password = new StandardUtils.Secret(mssqlBuilder.UserID, mssqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mssqlBuilder.ConnectionString;
                    break;
#if MYSQL
                case DatabaseType.MySQL:
                    var mysqlBuilder = new MySqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(mysqlBuilder.Server))
                        databaseOptions.Address = mysqlBuilder.Server;
                    if (mysqlBuilder.Port != 0)
                        databaseOptions.Port = (int)mysqlBuilder.Port;
                    if (!string.IsNullOrEmpty(mysqlBuilder.Database))
                        databaseOptions.Name = mysqlBuilder.Database;
                    if (!string.IsNullOrEmpty(mysqlBuilder.UserID) && !string.IsNullOrEmpty(mysqlBuilder.Password))
                    {
                        databaseOptions.User = mysqlBuilder.UserID;
                        databaseOptions.Password = new StandardUtils.Secret(mysqlBuilder.UserID, mysqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mysqlBuilder.ConnectionString;
                    break;
#endif
                case DatabaseType.PostgreSQL:
                    var pgBuilder = new NpgsqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(pgBuilder.Host))
                    {
                        databaseOptions.Address = pgBuilder.Host;
                    }
                    if (pgBuilder.Port != 0)
                    {
                        databaseOptions.Port = pgBuilder.Port;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.Database))
                    {
                        databaseOptions.Name = pgBuilder.Database;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.Username) && !string.IsNullOrEmpty(pgBuilder.Password))
                    {
                        databaseOptions.User = pgBuilder.Username;
                        databaseOptions.Password = new StandardUtils.Secret(pgBuilder.Username, pgBuilder.Password).Encrypted;
                    }
                    if (!string.IsNullOrEmpty(pgBuilder.SearchPath))
                    {
                        databaseOptions.Schema = pgBuilder.SearchPath;
                    }
                    databaseOptions.ConnectOptions = pgBuilder.ConnectionString;
                    break;

                case DatabaseType.Snowflake:
                    var snowflakeBuilder = new SnowflakeDbConnectionStringBuilder();
                    snowflakeBuilder.ConnectionString = connectionString;
                    if (snowflakeBuilder.ContainsKey("Account"))
                        databaseOptions.Address = snowflakeBuilder["Account"] as string;
                    if (snowflakeBuilder.ContainsKey("Database"))
                        databaseOptions.Name = snowflakeBuilder["Database"] as string;
                    if (snowflakeBuilder.ContainsKey("User") && snowflakeBuilder.ContainsKey(PASSWORD_PARAM))
                    {
                        databaseOptions.User = snowflakeBuilder["User"] as string;
                        var pwd = snowflakeBuilder[PASSWORD_PARAM] as string;
                        databaseOptions.Password = new Secret(databaseOptions.User!, pwd!).Encrypted;
                    }
                    databaseOptions.ConnectOptions = snowflakeBuilder.ConnectionString;
                    break;
                default:
                    throw new NotImplementedException("Not a supported database platform");
            }

            return databaseOptions;
        }

        public string BuildConnectionString(CSG.Adapter.Configuration.Database databaseOptions)
        {
            const string appName = "Genesys Cloud Data Adapter";
            string connectionString = "";

            if (databaseOptions.User is null)
                throw new ArgumentNullException("User", "Database user is not set.");
            if (databaseOptions.Password is null)
                throw new ArgumentNullException(PASSWORD_PARAM, "Database password is not set.");

            switch (databaseOptions.Type)
            {
                case DatabaseType.MSSQL:
                    // https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlclient.sqlconnectionstringbuilder
                    var mssqlBuilder = new SqlConnectionStringBuilder(
                        "Connection Timeout=60;" +
                        databaseOptions.ConnectOptions);

                    if (databaseOptions.Port == 0)
                        mssqlBuilder.DataSource = databaseOptions.Address;
                    else
                        mssqlBuilder.DataSource = string.Format("{0},{1}", databaseOptions.Address, databaseOptions.Port);
                    mssqlBuilder.InitialCatalog = databaseOptions.Name;
                    mssqlBuilder.UserID = databaseOptions.User;
                    mssqlBuilder.Password = new StandardUtils.Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    mssqlBuilder.ApplicationName = appName;

                    connectionString = mssqlBuilder.ToString();
                    mssqlBuilder.Password = "***"; // Can't use constant due to property name
                    _logger?.LogDebug(CONNECTION_STRING_FORMAT, mssqlBuilder.ToString());
                    break;
#if MYSQL
                case DatabaseType.MySQL:
                    // https://dev.mysql.com/doc/dev/connector-net/6.10/html/T_MySql_Data_MySqlClient_MySqlConnectionStringBuilder.htm
                    var mysqlBuilder = new MySqlConnectionStringBuilder(
                        "SSL Mode=Required;" +
                        databaseOptions.ConnectOptions);

                    mysqlBuilder.Server = databaseOptions.Address;
                    if (databaseOptions.Port != 0)
                        mysqlBuilder.Port = (uint)databaseOptions.Port;
                    mysqlBuilder.Database = databaseOptions.Name;
                    mysqlBuilder.UserID = databaseOptions.User;
                    mysqlBuilder.Password = new StandardUtils.Secret(databaseOptions.User, databaseOptions.Password).PlainText;

                    databaseOptions.Port = (int)mysqlBuilder.Port;
                    if (!string.IsNullOrEmpty(mysqlBuilder.Database))
                        databaseOptions.Name = mysqlBuilder.Database;
                    if (!string.IsNullOrEmpty(mysqlBuilder.UserID) && !string.IsNullOrEmpty(mysqlBuilder.Password))
                    {
                        databaseOptions.User = mysqlBuilder.UserID;
                        databaseOptions.Password = new StandardUtils.Secret(mysqlBuilder.UserID, mysqlBuilder.Password).Encrypted;
                    }
                    databaseOptions.ConnectOptions = mysqlBuilder.ConnectionString;

                    connectionString = mysqlBuilder.ToString();
                    mysqlBuilder.Password = "***"; // Can't use constant due to property name
                    _logger?.LogDebug(CONNECTION_STRING_FORMAT, mysqlBuilder.ToString());
                    break;
#endif
                case DatabaseType.PostgreSQL:
                    // https://www.npgsql.org/doc/connection-string-parameters.html
                    // https://www.npgsql.org/doc/api/Npgsql.NpgsqlConnectionStringBuilder.html

                    // https://www.postgresql.org/docs/current/ddl-schemas.html
                    // The first schema named in the search path is called the
                    // current schema. Aside from being the first schema
                    // searched, it is also the schema in which new tables will
                    // be created if the CREATE TABLE command does not specify a
                    // schema name.

                    var pgBuilder = new NpgsqlConnectionStringBuilder(
                        "Timeout=180;Connection Idle Lifetime=300;Command Timeout=180;" +
                        databaseOptions.ConnectOptions);

                    pgBuilder.Host = databaseOptions.Address;
                    if (pgBuilder.Port != 0)
                        pgBuilder.Port = databaseOptions.Port;
                    pgBuilder.Database = databaseOptions.Name;
                    pgBuilder.Username = databaseOptions.User;
                    pgBuilder.Password = new StandardUtils.Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    pgBuilder.SearchPath = databaseOptions.Schema;
                    pgBuilder.ApplicationName = appName;

                    connectionString = pgBuilder.ToString();
                    pgBuilder.Password = "***"; // Can't use constant due to property name
                    _logger?.LogDebug(CONNECTION_STRING_FORMAT, pgBuilder.ToString());
                    break;
                case DatabaseType.Snowflake:
                    var snowflakeBuilder = new SnowflakeDbConnectionStringBuilder();
                    snowflakeBuilder.ConnectionString = databaseOptions.ConnectOptions;

                    snowflakeBuilder["Account"] = databaseOptions.Address?.TrimEnd(".snowflakecomputing.com".ToCharArray());
                    if (!snowflakeBuilder.ContainsKey("Port"))
                        snowflakeBuilder["Port"] = databaseOptions.Port;
                    snowflakeBuilder["DB"] = databaseOptions.Name;
                    snowflakeBuilder["User"] = databaseOptions.User;
                    snowflakeBuilder["Password"] = new StandardUtils.Secret(databaseOptions.User, databaseOptions.Password).PlainText;
                    snowflakeBuilder["Schema"] = databaseOptions.Schema;

                    connectionString = snowflakeBuilder.ToString();
                    snowflakeBuilder[PASSWORD_PARAM] = "***";
                    _logger?.LogDebug(CONNECTION_STRING_FORMAT, snowflakeBuilder.ToString());
                    break;
                default:
                    throw new NotImplementedException("Not a supported database platform");
            }

            return connectionString;
        }
#nullable restore

        public void ConnectToDatabase()
        {
            // Make sure we have a connection string and database type
            if (string.IsNullOrEmpty(DBConnectionString))
            {
                // Try to initialize the connection string
                if (!ReadDBConnectionString())
                {
                    throw new InvalidOperationException("Database connection string is not initialized");
                }
            }

            // Check if we already have a connection and it's open
            if (DBConnect != null && DBConnect.State == ConnectionState.Open)
            {
                // Connection is already open, no need to reconnect
                return;
            }

            // If connection is closed or doesn't exist, create a new one
            if (DBConnect != null)
            {
                // Try to close and dispose the existing connection before creating a new one
                try
                {
                    DBConnect.Close();
                    DBConnect.Dispose();
                }
                catch (Exception ex)
                {
                    // Log but continue - we'll create a new connection anyway
                    if (_logger != null)
                        _logger?.LogWarning(ex, "Error closing existing database connection");
                    else
                        Console.WriteLine($"Error closing existing database connection: {ex.Message}");
                }
            }

            try
            {
                // Create a new connection based on database type
                switch (DBType)
                {
                    case DatabaseType.MSSQL:
                        DBConnect = new SqlConnection(DBConnectionString);
                        DBDataAdapter = new SqlDataAdapter();
                        break;
                    case DatabaseType.MySQL:
                        DBConnect = new MySqlConnection(DBConnectionString);
                        DBDataAdapter = new MySql.Data.MySqlClient.MySqlDataAdapter();
                        break;
                    case DatabaseType.PostgreSQL:
                        DBConnect = new NpgsqlConnection(DBConnectionString);
                        DBDataAdapter = new NpgsqlDataAdapter();
                        break;
                    case DatabaseType.Snowflake:
                        DBConnect = new SnowflakeDbConnection(DBConnectionString);
                        DBDataAdapter = new SnowflakeDbDataAdapter();
                        break;
                    default:
                        throw new NotImplementedException("Unsupported database type: " + DBType);
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogError(ex, "Failed to create database connection for type {DBType}", DBType);
                else
                    Console.WriteLine($"Failed to create database connection for type {DBType}: {ex.Message}");
                throw;
            }

            var timer = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                _logger?.LogDebug("Opening database connection to {Host}:{Port}/{Database}",
                    DBConnect.DataSource, DBConnect.Database, DBConnect.Database);
                DBConnect.Open();
                _logger?.LogDebug("Database connection opened successfully in {ElapsedMs}ms", timer.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    _logger?.LogWarning(
                        ex,
                        "Failed to connect to database, timeout {0}, took {1}",
                        DBConnect.ConnectionTimeout,
                        timer.Elapsed.ToString());
                }
                else
                {
                    Console.WriteLine("Failed to connect to database, timeout {0}, took {1}",
                        DBConnect.ConnectionTimeout,
                        timer.Elapsed.ToString());
                    Console.WriteLine(ex.ToString());
                }
                throw;
            }

            if (timer.Elapsed > TimeSpan.FromSeconds(DBConnect.ConnectionTimeout * 0.75))
                Console.WriteLine("WARNING: Connect to database took {0}, timeout {1}",
                        timer.Elapsed.ToString(),
                        DBConnect.ConnectionTimeout);
            timer.Restart();

            // TODO: Look into removing PostgresSchema in favour of a search path on the connection string.
            if (DBType == DatabaseType.PostgreSQL)
            {
                DbCommand cmd = DBConnect.CreateCommand();
                cmd.CommandText = "SET search_path = '" + PostgresSchema + "';";
                cmd.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// Explicitly close the database connection
        /// </summary>
        /// <remarks>
        /// This method is kept for backward compatibility.
        /// It calls CloseConnectionAtEndOfJob internally.
        /// </remarks>
        public void CloseConnectionToDatabase()
        {
            CloseConnectionAtEndOfJob();
        }

        /// <summary>
        /// Internal method to close the database connection
        /// </summary>
        internal void CloseConnection()
        {
            if (DBConnect != null)
            {
                try
                {
                    if (DBConnect.State == ConnectionState.Open)
                    {
                        _logger?.LogDebug("Closing database connection");
                        DBConnect.Close();
                    }
                    DBConnect.Dispose();
                    DBConnect = null;
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error closing database connection");
                }
            }
        }

        /// <summary>
        /// Explicitly close the database connection at the end of a job
        /// </summary>
        /// <remarks>
        /// This method should be called at the end of a job to ensure all connections are properly closed.
        /// During normal operation, connections are kept open for connection pooling.
        /// </remarks>
        public void CloseConnectionAtEndOfJob()
        {
            _logger?.LogDebug("Explicitly closing database connection at end of job");
            CloseConnection();
        }

        /// <summary>
        /// Log the current status of the Npgsql connection pool
        /// </summary>
        public void LogConnectionPoolStatus()
        {
            // Use the ConnectionManager to log pool status
            ConnectionManager.LogPoolStatus();
        }

        internal bool ReadDBConnectionString()
        {
            DBConnectionString = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_SQLCONNECTIONSTRING");
            /*
            Utils Util = new Utils();
            try
            {
                UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
                DBConnectionStringEncrypted = Util.ReadSetting("CSG_SQLCONNECTIONSTRING");
                DBConnectionString = UCAEncryption.DecryptData(DBConnectionStringEncrypted);
            }
            catch (Exception ex)
            {
                if (_logger != null)
                    _logger?.LogWarning(ex, "DB Unencrypt the Connection String Error");
                else
                    Console.WriteLine("DB Unencrypt the Connection String Error: {0}", ex.ToString());

                throw;
            }
            */

            return true;
        }

        public DataTable CreateInMemTable(string TableName)
        {
            string SQLStatement = string.Empty;
            switch (DBType)
            {
                case DatabaseType.MSSQL:
                    SQLStatement = "SELECT TOP (0) * FROM " + TableName;
                    break;
                case DatabaseType.MySQL:
                case DatabaseType.Snowflake:
                    SQLStatement = "SELECT  * FROM " + TableName + " LIMIT 0";
                    break;
                case DatabaseType.PostgreSQL:
                    SQLStatement = "SELECT  * FROM " + TableName.ToLower() + " LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            _logger?.LogTrace("SQL Statement {0}", SQLStatement);
            try
            {
                // Get the table schema
                DataTable DTTemp = GetSQLTableData(SQLStatement, TableName).Clone();

                DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns[0] };

                return DTTemp;
            }
            catch (Exception ex)
            {
                if (!SurpressErrors)
                {
                    _logger?.LogTrace("SQL Statement {0}", SQLStatement);
                    if (_logger != null)
                        _logger?.LogWarning(ex, "Issue Creating Blank Memory Table: {TableName}", TableName);
                    else
                        Console.WriteLine("Issue Creating Blank Memory Table :{0}\nError:{1}\nInner:{2}", TableName, ex.ToString(), ex.InnerException);
                }
                return null;
            }
        }

        public void DeleteSchedData(DateTime StartDate, DateTime EndDate)
        {
            ConnectToDatabase();
            try
            {
                DBDeleteCommand = DBConnect.CreateCommand();

                string DeleteString = String.Empty;

                if (DBConnect.State == ConnectionState.Open)
                {
                    switch (DBType)
                    {
                        case DatabaseType.MSSQL:
                        case DatabaseType.MySQL:
                            DeleteString = "Delete a from scheduleData a Inner Join ( select distinct sd.shiftid,sd.scheduleid from scheduleData sd where sd.activitystartdate between @startdate and @enddate) b on  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid where a.shiftstartdate between @startdate and @enddate";
                            break;
                        case DatabaseType.PostgreSQL:
                            DeleteString = "DELETE FROM scheduledata a using ( select distinct sd.shiftid,sd.scheduleid from scheduledata sd where sd.activitystartdate between @startdate and @enddate) b where  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid and a.shiftstartdate between @startdate and @enddate";
                            break;
                        case DatabaseType.Snowflake:
                            DeleteString = "DELETE FROM scheduledata a using ( select distinct sd.shiftid,sd.scheduleid from scheduledata sd where sd.activitystartdate between :startdate and :enddate) b where  a.shiftid = b.shiftid and a.scheduleid = b.scheduleid and a.shiftstartdate between :startdate and :enddate";
                            break;
                        default:
                            throw new NotImplementedException("Database type is not implemented");
                    }
                    DBDeleteCommand.CommandText = DeleteString;

                    IDbDataParameter DeleteParameter;

                    DeleteParameter = DBDeleteCommand.CreateParameter();
                    DeleteParameter.ParameterName = "startdate";
                    DeleteParameter.Value = StartDate;
                    if (DBType == DatabaseType.Snowflake)
                    {
                        // https://learn.microsoft.com/en-us/dotnet/api/system.data.dbtype
                        // "If the type is not specified, ADO.NET infers the data provider Type of the Parameter from the
                        // Value property of the Parameter object."
                        // Snowflake does not appear to implement automatic type mapping for bind variables.
                        // Dapper uses a manually defined lookup, so will manually assign.
                        // https://github.com/DapperLib/Dapper/blob/4fb1ea29d490d13251b0135658ecc337aeb60cdb/Dapper/SqlMapper.cs#L169
                        // When this is moved to a generic implementation of parameters, fully define the mapping.
                        DeleteParameter.DbType = DbType.DateTime;
                    }
                    DBDeleteCommand.Parameters.Add(DeleteParameter);

                    DeleteParameter = DBDeleteCommand.CreateParameter();
                    DeleteParameter.ParameterName = "enddate";
                    DeleteParameter.Value = EndDate;
                    if (DBType == DatabaseType.Snowflake)
                        DeleteParameter.DbType = DbType.DateTime;
                    DBDeleteCommand.Parameters.Add(DeleteParameter);

                    DBDeleteCommand.ExecuteNonQuery();
                }
            }
            finally
            {
                if (DBConnect?.State == ConnectionState.Open)
                    CloseConnectionToDatabase();
            }
        }






    }
}
// spell-checker: ignore: tinyint, Npgsql, shiftid
