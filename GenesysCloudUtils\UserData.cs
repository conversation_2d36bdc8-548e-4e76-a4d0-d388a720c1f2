using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using DBUtils;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PresDef = GenesysCloudDefDetailedPresence;
using PresDefJob = GenesysCloudDefUserPresenceDetailedJobs;
using Presence = GenesysCloudDefPresence;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class UserData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime UserPresenceLastUpdate { get; set; }
        public DateTime UserInteractionLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        public string AggInterval { get; set; }
        private ILogger _logger;

        public UserData(ILogger logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            try
            {
                _logger?.LogDebug("Initializing UserData");
                GCUtilities.Initialize();
                DBUtil.Initialize();

                UCAUtils = new StandardUtils.Utils();
                CustomerKeyID = GCUtilities.CustomerKeyID;
                UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
                GCControlData = GCUtilities.GCControlData;
                _logger?.LogInformation("GC User Data - Obtaining API Key...");
                GCApiKey = GCUtilities.GCApiKey;
                _logger?.LogInformation("Initialization completed successfully.");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Initialization Error");
                throw;
            }
        }

        public DataTable GetHoursBlockData(int MonthOffset)
        {
            DataTable HoursBookedData = null;

            try
            {
                DataTable DTTemp = new DataTable();

                string FromDate = DateTime.Now.AddMonths(-MonthOffset).ToString("yyyy-MM-01T00:00:00");
                string ToDate = DateTime.Parse(FromDate).AddMonths(1).AddDays(-1).ToString("yyyy-MM-ddT23:59:59");

                string SQLString = string.Empty;

                switch (DBUtil.DBType)
                {
                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "DATENAME(WEEKDAY, startdateltc) AS dow, up.presencetime, " +
                                    "CAST(up.startdate AS DATE) AS actualdate, up.startdate, up.startdateltc " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "TO_CHAR(startdateltc, 'Dy') AS dow, up.presencetime, " +
                                    "up.startdate::timestamp::date AS actualdate, up.startdate::timestamp, up.startdateltc::timestamp " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "DAYOFWEEK(up.startdateltc) AS dow, up.presencetime, " +
                                    "CAST(up.startdate AS DATE) AS actualdate, up.startdate, up.startdateltc " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate";
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }

                // Use parameterized queries to prevent SQL injection
                var parameters = new Dictionary<string, object>
                {
                    { "@FromDate", FromDate },
                    { "@ToDate", ToDate }
                };

                DTTemp = DBUtil.GetSQLTableData(SQLString, "Hours Block");
                _logger?.LogInformation("HoursBlockData: Retrieved {RowCount} Row(s)", DTTemp.Rows.Count);

                // Sort the data in memory by userid and startdate to replace the SQL ORDER BY clause
                // This improves database performance by removing the ORDER BY from the SQL query
                if (DTTemp != null && DTTemp.Rows.Count > 0)
                {
                    DataView sortedView = DTTemp.DefaultView;
                    sortedView.Sort = "userid ASC, startdate ASC";
                    DTTemp = sortedView.ToTable();
                    _logger?.LogDebug("HoursBlockData: Sorted {RowCount} rows in memory by userid and startdate", DTTemp.Rows.Count);
                }

                HoursBookedData = DBUtil.CreateInMemTable("hoursblockdata");

                bool FirstRecord = true;
                bool WriteRecord = false;

                string OldUserId = "";
                DateTime OldDayStart = DateTime.MinValue;
                DateTime OldDayEnd = DateTime.MinValue;

                decimal TotalHrs = 0;
                decimal TotalBreak = 0;

                int HrsDifference = 0;

                int Counter = 0;

                foreach (DataRow HrsRow in DTTemp.Rows)
                {
                    string userId = HrsRow["userid"].ToString();
                    DateTime startDate = (DateTime)HrsRow["startdate"];
                    decimal presenceTime = (decimal)HrsRow["presencetime"];
                    double timeDiffSeconds = OldDayEnd == DateTime.MinValue ? 0 : (startDate - OldDayEnd).TotalSeconds;
                    bool isDateChanged = OldDayStart.Date != startDate.Date;

                    // Structured and concise log message
                    string logMessage = $"UserID: {userId} | Date: {startDate:yyyy-MM-dd} | PresenceTime: {presenceTime} hrs | " +
                                        $"TimeDiff: {timeDiffSeconds} sec | TotalHrs: {TotalHrs} hrs | " +
                                        $"DateChanged: {isDateChanged} | From: {OldDayStart:yyyy-MM-dd} To: {OldDayEnd:yyyy-MM-dd}";

                    if (_logger != null && _logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug(logMessage);
                    }

                    Counter++;

                    // Check for presence gap greater than 30 minutes or date change
                    if (!FirstRecord &&
                        (timeDiffSeconds > 1800 || isDateChanged))
                    {
                        WriteRecord = true;
                        _logger?.LogDebug("Triggered Condition: Presence gap > 30 minutes or date changed.");
                    }

                    // Check for User ID change
                    if (OldUserId != userId)
                    {
                        if (FirstRecord)
                        {
                            OldUserId = userId;
                            OldDayStart = (DateTime)HrsRow["startdateltc"];

                            TimeSpan ts = (DateTime)HrsRow["startdateltc"] - (DateTime)HrsRow["startdate"];
                            HrsDifference = Convert.ToInt32(ts.TotalMinutes);
                            TotalHrs = 0;
                            TotalBreak = 0;
                            FirstRecord = false;
                        }
                        else
                        {
                            WriteRecord = true;
                            _logger?.LogDebug("Triggered Condition: User ID changed.");
                        }
                    }

                    if (WriteRecord)
                    {
                        try
                        {
                            DataRow NewRow = HoursBookedData.NewRow();

                            // Using standardized null value handling with ternary operator pattern
                            NewRow["keyid"] = $"{OldUserId}|{OldDayStart:yyyy-MM-ddTHH:mm:ss}";
                            NewRow["userid"] = OldUserId != null ? OldUserId : DBNull.Value;
                            NewRow["startdate"] = OldDayStart != DateTime.MinValue ? OldDayStart.AddMinutes(-HrsDifference) : DBNull.Value;
                            NewRow["startdateltc"] = OldDayStart != DateTime.MinValue ? OldDayStart : DBNull.Value;
                            NewRow["enddate"] = OldDayEnd != DateTime.MinValue ? OldDayEnd.AddMinutes(-HrsDifference + 30) : DBNull.Value;
                            NewRow["enddateltc"] = OldDayEnd != DateTime.MinValue ? OldDayEnd.AddMinutes(30) : DBNull.Value;
                            NewRow["totalhrs"] = TotalHrs;
                            NewRow["breakhrs"] = TotalBreak;

                            HoursBookedData.Rows.Add(NewRow);
                            _logger?.LogDebug("Row Written Successfully.");
                        }
                        catch (System.Data.ConstraintException ex)
                        {
                            _logger?.LogWarning(ex, "Duplicate row detected. Skipping addition.");
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error adding row");
                        }

                        OldUserId = userId;
                        OldDayStart = (DateTime)HrsRow["startdateltc"];
                        TotalHrs = 0;
                        TotalBreak = 0;
                        WriteRecord = false;
                    }

                    TotalHrs += presenceTime;
                    string systemPresenceId = HrsRow["systempresenceid"].ToString().ToLower();
                    if (systemPresenceId == "break" || systemPresenceId == "meal" || systemPresenceId == "meeting")
                    {
                        TotalBreak += presenceTime;
                    }
                    OldDayEnd = (DateTime)HrsRow["startdateltc"];
                }

                _logger?.LogInformation("GetHoursBlockData: Processing completed successfully.");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "GetHoursBlockData Error");
                throw;
            }

            return HoursBookedData;
        }

        public DataTable GetUserPresenceDataFromGC(DataTable Users, DataTable Presences, string StartDate, string EndDate)
        {
            DataTable UserPresence = null;
            int totalRowsProcessed = 0;

            try
            {
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                _logger?.LogInformation("Retrieving User Presence Data from {StartDate}, Current Last Update {LastUpdate}", StartDate, UserPresenceLastUpdate);

                // Create in-memory table for user presence data
                UserPresence = DBUtil.CreateInMemTable("userPresenceData");
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                string OnQueueId = string.Empty;
                DataRow DRPresence = Presences.Select("systempresence='On Queue'").FirstOrDefault();

                if (DRPresence != null)
                {
                    OnQueueId = DRPresence["id"].ToString();
                }

                // Check if there are any users to process
                if (Users.Rows.Count == 0)
                {
                    _logger?.LogInformation("No users found to process for user presence data. Returning empty result.");
                    return UserPresence;
                }

                StringBuilder SearchUsers = new StringBuilder();

                foreach (DataRow DRUser in Users.Rows)
                {
                    string userId = DRUser["id"]?.ToString();
                    if (!string.IsNullOrEmpty(userId))
                    {
                        SearchUsers.Append("{\"dimension\": \"userId\", \"value\": \"" + userId + "\"},");
                    }
                }

                if (SearchUsers.Length > 0)
                    SearchUsers.Length--;

                // Check if we have any valid user IDs after filtering
                if (SearchUsers.Length == 0)
                {
                    _logger?.LogInformation("No valid user IDs found to create predicates for user presence data. Users table has {UserCount} rows but no valid IDs.", Users.Rows.Count);
                    return UserPresence;
                }

                _logger?.LogDebug("User presence API request will include {PredicateCount} user predicates", SearchUsers.ToString().Split(',').Length);

                // Format granularity correctly for Genesys Cloud API (ISO-8601 duration format)
                string formattedGranularity = AggInterval.StartsWith("PT") ? AggInterval : $"PT{AggInterval}M";

                _logger?.LogDebug("User presence API request granularity: Original='{OriginalGranularity}', Formatted='{FormattedGranularity}'", AggInterval, formattedGranularity);

                string RequestBody = "{ " +
                                     $"\"interval\": \"{StartDate}/{EndDate}\"," +
                                     $"\"granularity\": \"{formattedGranularity}\"," +
                                     "\"groupBy\": [\"userId\"]," +
                                     "\"metrics\": [\"tOrganizationPresence\", \"tAgentRoutingStatus\"]," +
                                     "\"filter\": {\"type\": \"or\", \"predicates\": [" + SearchUsers.ToString() + "]}" +
                                     "}";

                _logger?.LogDebug("User presence API request body: {RequestBody}", RequestBody);

                string JsonString = string.Empty;
                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/aggregates/query", GCApiKey, RequestBody);

                    // Validate response before proceeding - fail-fast for non-JSON responses
                    if (!string.IsNullOrWhiteSpace(JsonString))
                    {
                        string trimmedResponse = JsonString.TrimStart();
                        if (!trimmedResponse.StartsWith('{') && !trimmedResponse.StartsWith('['))
                        {
                            _logger?.LogError("API returned non-JSON response in GetUserPresenceDataFromGC. Response preview: {ResponsePreview}",
                                JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                            throw new HttpRequestException($"API returned non-JSON content: {JsonString.Substring(0, Math.Min(JsonString.Length, 200))}");
                        }
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    _logger?.LogError(httpEx, "HTTP error in GetUserPresenceDataFromGC: {Message}", httpEx.Message);

                    // Check if this is a BadRequest (HTTP 400) which should halt processing
                    if (httpEx.Message.Contains("400") || httpEx.Message.Contains("Bad Request"))
                    {
                        _logger?.LogError("HTTP 400 Bad Request detected - halting processing");
                        throw; // Rethrow to halt processing
                    }

                    // For other HTTP errors, return empty table to allow retry logic to handle
                    return UserPresence;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "API Call Error in GetUserPresenceDataFromGC");
                    throw;
                }

                // Check if the response is an error response
                if (JsonString != null && (JsonString.Contains("\"error\":") || JsonString.Contains("\"statusCode\":") || JsonString.Contains("\"message\":")))
                {
                    _logger?.LogError("API Error: {Response}", JsonString);

                    // Return the empty table instead of trying to deserialize the error response
                    // The rate limit handling is now done in JsonReturnString through the centralized HandleRateLimitAndRetry method
                    return UserPresence;
                }

                if (JsonString.Length > 10)
                {
                    Presence.UserPresenceData UserData = null;
                    try
                    {
                        UserData = JsonConvert.DeserializeObject<Presence.UserPresenceData>(JsonString,
                                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        // Include problematic JSON in the error message
                        string jsonPreview = JsonString.Substring(0, Math.Min(200, JsonString.Length));
                        _logger?.LogError(jsonEx, "JSON Deserialization Error in GetUserPresenceDataFromGC. Problematic JSON: {Json}...", jsonPreview);
                        throw;
                    }

                    foreach (Presence.UserPresenceResults Results in UserData.results)
                    {
                        foreach (Presence.Datum TimeDataGroup in Results.data)
                        {
                            string TimeInterval = TimeDataGroup.interval.Split('/')[0];
                            foreach (Presence.Metric TimeData in TimeDataGroup.metrics)
                            {
                                try
                                {
                                    DataRow NewPresenceDataRow = UserPresence.NewRow();
                                    // ParseExact with 'Z' format already indicates UTC, use ToUtcSafe to avoid double conversion
                                    DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUtcSafe();

                                    if (MaxUpdateDateTest > UserPresenceLastUpdate)
                                        UserPresenceLastUpdate = MaxUpdateDateTest;

                                    // Using standardized null value handling with ternary operator pattern
                                    NewPresenceDataRow["userid"] = Results.group.userId != null ? Results.group.userId : DBNull.Value;
                                    NewPresenceDataRow["id"] = TimeData.qualifier != null ? TimeData.qualifier : DBNull.Value;
                                    NewPresenceDataRow["startdate"] = MaxUpdateDateTest;
                                    NewPresenceDataRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(MaxUpdateDateTest, AppTimeZone);

                                    string TempKeyid = TimeData.qualifier + "|" + Results.group.userId + "|" + TimeInterval;
                                    NewPresenceDataRow["keyid"] = TempKeyid != null ? TempKeyid : DBNull.Value;

                                    switch (TimeData.metric.Trim())
                                    {
                                        case "tAgentRoutingStatus":
                                            NewPresenceDataRow["timetype"] = "Routing";
                                            NewPresenceDataRow["presenceid"] = "On Queue";
                                            NewPresenceDataRow["systempresenceid"] = "On Queue";
                                            NewPresenceDataRow["id"] = OnQueueId;

                                            if (TimeData.stats.sum % 100 == 0)
                                                TimeData.stats.sum += 10;
                                            NewPresenceDataRow["presencetime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingtime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingid"] = TimeData.qualifier;
                                            break;

                                        case "tOrganizationPresence":
                                            string PresenceString = string.Empty;
                                            NewPresenceDataRow["timetype"] = "Presence";

                                            DRPresence = Presences.Select("id='" + TimeData.qualifier + "'").FirstOrDefault();

                                            if (DRPresence != null)
                                            {
                                                if (DRPresence["systempresence"].ToString() == DRPresence["orgpresence"].ToString())
                                                    PresenceString = DRPresence["systempresence"].ToString();
                                                else
                                                    PresenceString = DRPresence["systempresence"].ToString() + "-" + DRPresence["orgpresence"].ToString();

                                                NewPresenceDataRow["presenceid"] = PresenceString;
                                                NewPresenceDataRow["systempresenceid"] = DRPresence["systempresence"].ToString();
                                            }
                                            else
                                            {
                                                NewPresenceDataRow["presenceid"] = TimeData.qualifier;
                                                NewPresenceDataRow["systempresenceid"] = TimeData.qualifier;
                                            }

                                            if (TimeData.stats.sum % 100 == 0)
                                                TimeData.stats.sum += 10;

                                            NewPresenceDataRow["presencetime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingtime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingid"] = "Off Queue";
                                            break;
                                    }

                                    if (NewPresenceDataRow["presenceid"].ToString() == "On Queue" &&
                                        NewPresenceDataRow["routingid"].ToString() == "Off Queue")
                                    {
                                        NewPresenceDataRow["routingid"] = "On Queue";
                                    }

                                    if (NewPresenceDataRow["presenceid"].ToString() != "Offline")
                                    {
                                        UserPresence.Rows.Add(NewPresenceDataRow);
                                        totalRowsProcessed++;

                                        // Log a summary every 50 rows
                                        if (totalRowsProcessed % 50 == 0)
                                        {
                                            _logger?.LogDebug("User presence data processing: {RowCount} rows processed so far", totalRowsProcessed);
                                        }
                                    }
                                }
                                catch (System.Data.ConstraintException)
                                {
                                    // Duplicate row, ignore
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogError(ex, "Error adding row in GetUserPresenceDataFromGC");
                                    Environment.ExitCode = -15000;
                                }
                            }
                        }
                    }


                }
                else
                {
                    _logger?.LogWarning("Received an empty or invalid response from the API in GetUserPresenceDataFromGC.");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "GetUserPresenceDataFromGC Error");
                Environment.ExitCode = -15000;
                throw;
            }

            // Log a final summary
            _logger?.LogInformation("User presence data processing completed. Total rows processed: {RowCount}", totalRowsProcessed);
            return UserPresence;
        }

        /// <summary>
        /// Gets user interaction data from Genesys Cloud for the specified date range.
        /// </summary>
        /// <param name="StartDate">The start date in ISO 8601 format.</param>
        /// <param name="EndDate">The end date in ISO 8601 format.</param>
        /// <param name="AggViews">The aggregation views configuration.</param>
        /// <param name="granularity">Optional granularity in ISO-8601 duration format (e.g., PT15M, PT30M, PT1H). If not provided, uses the default AggInterval from configuration.</param>
        /// <returns>A DataTable containing user interaction data.</returns>
        public DataTable GetUserInteractionDataFromGC(string StartDate, string EndDate, string AggViews, string granularity = null)
        {
            DataTable UserInteraction = null;
            int totalRowsProcessed = 0;

            try
            {
                // Use provided granularity or fall back to the default AggInterval
                string intervalValue = string.IsNullOrEmpty(granularity) ? AggInterval : granularity;

                // Ensure granularity is in proper ISO-8601 format for Genesys Cloud API
                if (!string.IsNullOrEmpty(intervalValue))
                {
                    // If intervalValue is just a number (legacy format), convert to ISO-8601
                    if (int.TryParse(intervalValue, out int minutes))
                    {
                        intervalValue = $"PT{minutes}M";
                        _logger?.LogDebug("Converted legacy granularity format '{LegacyFormat}' to ISO-8601 format '{Iso8601Format}'",
                            granularity ?? AggInterval, intervalValue);
                    }
                    // If it doesn't start with PT, assume it's minutes and format correctly
                    else if (!intervalValue.StartsWith("PT"))
                    {
                        // Try to extract number from string like "30M" or "30"
                        string numericPart = new string(intervalValue.Where(char.IsDigit).ToArray());
                        if (int.TryParse(numericPart, out int extractedMinutes))
                        {
                            intervalValue = $"PT{extractedMinutes}M";
                            _logger?.LogDebug("Formatted granularity '{OriginalFormat}' to ISO-8601 format '{Iso8601Format}'",
                                granularity ?? AggInterval, intervalValue);
                        }
                        else
                        {
                            // Default to 30 minutes if we can't parse
                            intervalValue = "PT30M";
                            _logger?.LogWarning("Unable to parse granularity '{InvalidFormat}', defaulting to PT30M",
                                granularity ?? AggInterval);
                        }
                    }

                    // Validate minimum granularity (1 minute)
                    try
                    {
                        TimeSpan duration = System.Xml.XmlConvert.ToTimeSpan(intervalValue);
                        if (duration.TotalMinutes < 1)
                        {
                            intervalValue = "PT1M";
                            _logger?.LogWarning("Granularity was less than 1 minute, adjusted to PT1M");
                        }
                    }
                    catch (Exception ex)
                    {
                        intervalValue = "PT30M";
                        _logger?.LogWarning(ex, "Invalid granularity format, defaulting to PT30M");
                    }
                }
                else
                {
                    intervalValue = "PT30M"; // Default fallback
                    _logger?.LogDebug("No granularity specified, using default PT30M");
                }

                _logger?.LogInformation("Starting data retrieval from {StartDate} to {EndDate} with granularity {Granularity}",
                    StartDate, EndDate, intervalValue);

                TimeZoneInfo AppTimeZone;
                try
                {
                    AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                    Console.WriteLine($"Time zone '{TimeZoneConfig}' successfully retrieved.");
                }
                catch (TimeZoneNotFoundException)
                {
                    Console.WriteLine($"Error: Time zone '{TimeZoneConfig}' not found.");
                    throw;
                }
                catch (InvalidTimeZoneException)
                {
                    Console.WriteLine($"Error: Time zone '{TimeZoneConfig}' is invalid.");
                    throw;
                }

                UserInteraction = DBUtil.CreateInMemTable("userInteractionData");
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                // First try with the full date range
                bool useChunking = false;
                string RequestBody = "{ " +
                                     $"\"interval\": \"{StartDate}/{EndDate}\"," +
                                     $"\"granularity\": \"{intervalValue}\"," +
                                     "\"groupBy\": [\"userId\", \"queueId\", \"mediaType\", \"direction\", \"wrapUpCode\"]," +
                                     "\"metrics\": [" +
                                     "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\"," +
                                     "\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\"," +
                                     "\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\"," +
                                     "\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\"," +
                                     "\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\"," +
                                     "\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\"," +
                                     "\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\"," +
                                     "\"tVoicemail\",\"tWait\"" +
                                     "]";

                StringBuilder AViews = new StringBuilder();
                if (!string.IsNullOrEmpty(AggViews))
                {
                    foreach (string AggregationViews in AggViews.Split(';'))
                    {
                        if (string.IsNullOrWhiteSpace(AggregationViews)) continue;

                        string[] Views = AggregationViews.Split(',');
                        if (Views.Length >= 4)
                        {
                            // Validate numeric values to prevent malformed JSON
                            if (int.TryParse(Views[1], out int gteValue) && int.TryParse(Views[2], out int ltValue))
                            {
                                AViews.Append($"{{\"target\": \"{Views[0]}\", \"name\": \"{Views[3]}\", \"function\": \"rangeBound\", \"range\": {{ \"gte\": {gteValue}, \"lt\": {ltValue} }} }},");
                            }
                            else
                            {
                                _logger?.LogWarning("Invalid numeric values in AggregationViews: '{AggregationViews}'. Skipping.", AggregationViews);
                            }
                        }
                        else
                        {
                            _logger?.LogWarning("Invalid AggregationViews format: '{AggregationViews}'. Expected format: target,gte,lt,name", AggregationViews);
                        }
                    }
                }

                if (AViews.Length > 0)
                {
                    AViews.Length--;
                    RequestBody += $", \"views\": [{AViews}]}}";
                }
                else
                {
                    RequestBody += "}";
                }

                // Enable request body logging for debugging HTTP 400 errors
                _logger?.LogDebug("User interaction data request body: {RequestBody}", RequestBody);

                // Validate request body JSON syntax before sending
                try
                {
                    var testParse = Newtonsoft.Json.JsonConvert.DeserializeObject(RequestBody);
                }
                catch (Newtonsoft.Json.JsonException jsonEx)
                {
                    _logger?.LogError(jsonEx, "Malformed JSON in request body for GetUserInteractionDataFromGC: {RequestBody}", RequestBody);
                    throw new HttpRequestException($"Invalid JSON request body: {jsonEx.Message}");
                }

                string JsonString = string.Empty;
                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);

                    // Validate response before proceeding - fail-fast for non-JSON responses
                    if (!string.IsNullOrWhiteSpace(JsonString))
                    {
                        string trimmedResponse = JsonString.TrimStart();
                        if (!trimmedResponse.StartsWith("{") && !trimmedResponse.StartsWith("["))
                        {
                            _logger?.LogError("API returned non-JSON response in GetUserInteractionDataFromGC. Response preview: {ResponsePreview}",
                                JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                            throw new HttpRequestException($"API returned non-JSON content: {JsonString.Substring(0, Math.Min(JsonString.Length, 200))}");
                        }
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    _logger?.LogError(httpEx, "HTTP error in GetUserInteractionDataFromGC: {Message}", httpEx.Message);

                    // Check if this is a BadRequest (HTTP 400) which should halt processing
                    if (httpEx.Message.Contains("400") || httpEx.Message.Contains("Bad Request"))
                    {
                        _logger?.LogError("HTTP 400 Bad Request detected - halting processing");
                        throw; // Rethrow to halt processing
                    }

                    // For other HTTP errors, return empty table to allow retry logic to handle
                    return UserInteraction;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("Result set is larger than result limit"))
                {
                    // This is a specific error we want to handle differently
                    _logger?.LogWarning("API limit exceeded in GetUserInteractionDataFromGC: {Message}", ex.Message);
                    throw; // Rethrow to be caught by the adaptive time span logic
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "API call failed in GetUserInteractionDataFromGC: {Message}", ex.Message);

                    // Check if the error is related to result set size
                    if (ex.Message.Contains("Result set is larger than result limit") ||
                        ex.Message.Contains("too many results") ||
                        ex.Message.Contains("result limit"))
                    {
                        _logger?.LogInformation("Detected result size limit error. Switching to chunked processing.");
                        useChunking = true;
                    }
                    else
                    {
                        // For other errors, just return the empty table
                        return UserInteraction;
                    }
                }

                // Check if the response is an error response related to result size
                if (!useChunking && JsonString != null &&
                    (JsonString.Contains("Result set is larger than result limit") ||
                     JsonString.Contains("too many results") ||
                     JsonString.Contains("result limit")))
                {
                    Console.WriteLine("Detected result size limit error in response. Switching to chunked processing.");
                    useChunking = true;
                }

                // If we need to use chunking, parse the dates and process in chunks
                if (useChunking)
                {
                    Console.WriteLine("Using chunked processing to handle large result set.");

                    // Parse start and end dates
                    if (!DateTime.TryParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime startDateTime))
                    {
                        Console.WriteLine($"Error: Invalid StartDate format '{StartDate}' in GetUserInteractionDataFromGC.");
                        return UserInteraction;
                    }

                    if (!DateTime.TryParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime endDateTime))
                    {
                        Console.WriteLine($"Error: Invalid EndDate format '{EndDate}' in GetUserInteractionDataFromGC.");
                        return UserInteraction;
                    }

                    // Calculate the maximum chunk size (6 hours)
                    TimeSpan chunkSize = TimeSpan.FromHours(6);

                    // Process data in chunks
                    for (DateTime chunkStart = startDateTime; chunkStart < endDateTime; chunkStart = chunkStart.Add(chunkSize))
                    {
                        // Calculate chunk end (either 6 hours later or the original end date, whichever is earlier)
                        DateTime chunkEnd = chunkStart.Add(chunkSize);
                        if (chunkEnd > endDateTime)
                        {
                            chunkEnd = endDateTime;
                        }

                        string chunkStartStr = chunkStart.ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string chunkEndStr = chunkEnd.ToString("yyyy-MM-ddTHH:00:00.000Z");

                        Console.WriteLine($"Processing chunk from {chunkStartStr} to {chunkEndStr}");

                        // Use the same groupBy fields as the original query
                        RequestBody = "{ " +
                                     $"\"interval\": \"{chunkStartStr}/{chunkEndStr}\"," +
                                     $"\"granularity\": \"{intervalValue}\"," +
                                     "\"groupBy\": [\"userId\", \"queueId\", \"mediaType\", \"direction\", \"wrapUpCode\"]," +
                                     "\"metrics\": [" +
                                     "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\"," +
                                     "\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\"," +
                                     "\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\"," +
                                     "\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\"," +
                                     "\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\"," +
                                     "\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\"," +
                                     "\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\"," +
                                     "\"tVoicemail\",\"tWait\"" +
                                     "]";

                        StringBuilder chunkAViews = new StringBuilder();
                        if (!string.IsNullOrEmpty(AggViews))
                        {
                            foreach (string AggregationViews in AggViews.Split(';'))
                            {
                                if (string.IsNullOrWhiteSpace(AggregationViews)) continue;

                                string[] Views = AggregationViews.Split(',');
                                if (Views.Length >= 4)
                                {
                                    // Validate numeric values to prevent malformed JSON
                                    if (int.TryParse(Views[1], out int gteValue) && int.TryParse(Views[2], out int ltValue))
                                    {
                                        chunkAViews.Append($"{{\"target\": \"{Views[0]}\", \"name\": \"{Views[3]}\", \"function\": \"rangeBound\", \"range\": {{ \"gte\": {gteValue}, \"lt\": {ltValue} }} }},");
                                    }
                                    else
                                    {
                                        _logger?.LogWarning("Invalid numeric values in chunk AggregationViews: '{AggregationViews}'. Skipping.", AggregationViews);
                                    }
                                }
                                else
                                {
                                    _logger?.LogWarning("Invalid chunk AggregationViews format: '{AggregationViews}'. Expected format: target,gte,lt,name", AggregationViews);
                                }
                            }
                        }

                        if (chunkAViews.Length > 0)
                        {
                            chunkAViews.Length--;
                            RequestBody += $", \"views\": [{chunkAViews}]}}";
                        }
                        else
                        {
                            RequestBody += "}";
                        }

                        // Enable request body logging for debugging HTTP 400 errors
                        _logger?.LogDebug("User interaction data chunk request body: {RequestBody}", RequestBody);

                        // Validate chunk request body JSON syntax before sending
                        try
                        {
                            var testParse = Newtonsoft.Json.JsonConvert.DeserializeObject(RequestBody);
                        }
                        catch (Newtonsoft.Json.JsonException jsonEx)
                        {
                            _logger?.LogError(jsonEx, "Malformed JSON in chunk request body for GetUserInteractionDataFromGC: {RequestBody}", RequestBody);
                            throw new HttpRequestException($"Invalid JSON chunk request body: {jsonEx.Message}");
                        }

                        string chunkJsonString = string.Empty;
                        try
                        {
                            chunkJsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);

                            // Validate response before proceeding - fail-fast for non-JSON responses
                            if (!string.IsNullOrWhiteSpace(chunkJsonString))
                            {
                                string trimmedResponse = chunkJsonString.TrimStart();
                                if (!trimmedResponse.StartsWith('{') && !trimmedResponse.StartsWith('['))
                                {
                                    _logger?.LogError("API returned non-JSON response for chunk {ChunkStart} to {ChunkEnd}. Response preview: {ResponsePreview}",
                                        chunkStartStr, chunkEndStr, chunkJsonString.Length > 200 ? chunkJsonString.Substring(0, 200) + "..." : chunkJsonString);
                                    // Continue to the next chunk instead of failing the entire process
                                    continue;
                                }
                            }
                        }
                        catch (HttpRequestException httpEx)
                        {
                            _logger?.LogError(httpEx, "HTTP error for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, httpEx.Message);

                            // Check if this is a BadRequest (HTTP 400) which should halt processing
                            if (httpEx.Message.Contains("400") || httpEx.Message.Contains("Bad Request"))
                            {
                                _logger?.LogError("HTTP 400 Bad Request detected for chunk - halting processing");
                                throw; // Rethrow to halt processing
                            }

                            // Check for rate limiting (HTTP 429) and implement retry logic
                            if (httpEx.Message.Contains("429") || httpEx.Message.Contains("TooManyRequests"))
                            {
                                int retryAttempts = 0;
                                const int maxRetryAttempts = 3;
                                bool chunkProcessed = false;

                                while (!chunkProcessed && retryAttempts < maxRetryAttempts)
                                {
                                    retryAttempts++;
                                    _logger?.LogWarning("Rate limit encountered for chunk {ChunkStart} to {ChunkEnd} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                        chunkStartStr, chunkEndStr, retryAttempts, maxRetryAttempts);

                                    // Get a new API key for rate limiting issues
                                    try
                                    {
                                        _logger?.LogInformation("Refreshing API key due to rate limiting for chunk {ChunkStart} to {ChunkEnd}", chunkStartStr, chunkEndStr);
                                        GCUtilities.GetGCAPIKey();
                                        GCApiKey = GCUtilities.GCApiKey;
                                    }
                                    catch (Exception keyEx)
                                    {
                                        _logger?.LogError(keyEx, "Failed to refresh API key for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, keyEx.Message);
                                    }

                                    // Implement exponential backoff with jitter
                                    int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                    _logger?.LogInformation("Waiting {WaitSeconds} seconds before retrying chunk {ChunkStart} to {ChunkEnd}", waitSeconds, chunkStartStr, chunkEndStr);
                                    Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));

                                    // Retry the API call
                                    try
                                    {
                                        chunkJsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);
                                        chunkProcessed = true;
                                        _logger?.LogInformation("Successfully processed chunk {ChunkStart} to {ChunkEnd} after {Attempts} retry attempts", chunkStartStr, chunkEndStr, retryAttempts);
                                        break; // Exit retry loop and continue with processing
                                    }
                                    catch (Exception retryEx)
                                    {
                                        _logger?.LogWarning(retryEx, "Retry attempt {Attempt} failed for chunk {ChunkStart} to {ChunkEnd}: {Message}", retryAttempts, chunkStartStr, chunkEndStr, retryEx.Message);

                                        if (retryAttempts >= maxRetryAttempts)
                                        {
                                            _logger?.LogError("Rate limiting exceeded retry limit for chunk {ChunkStart} to {ChunkEnd} after {Attempts} attempts - skipping chunk to preserve sync date progression",
                                                chunkStartStr, chunkEndStr, maxRetryAttempts);
                                            Console.WriteLine($"FAILED CHUNK: {chunkStartStr} to {chunkEndStr} - Rate limiting exceeded {maxRetryAttempts} retry attempts. Skipping to prevent sync date stagnation.");
                                            break; // Exit retry loop and continue to next chunk
                                        }
                                    }
                                }

                                if (!chunkProcessed)
                                {
                                    _logger?.LogError("Failed to process chunk {ChunkStart} to {ChunkEnd} after {Attempts} retry attempts - skipping chunk to preserve sync date progression",
                                        chunkStartStr, chunkEndStr, maxRetryAttempts);
                                    Console.WriteLine($"FAILED CHUNK: {chunkStartStr} to {chunkEndStr} - Failed after {maxRetryAttempts} retry attempts. Skipping to prevent sync date stagnation.");
                                    continue; // Skip this chunk and continue to next chunk
                                }
                            }
                            else
                            {
                                // For other HTTP errors that are not retryable, halt processing
                                _logger?.LogError("Non-retryable HTTP error for chunk {ChunkStart} to {ChunkEnd} - halting processing", chunkStartStr, chunkEndStr);
                                throw;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "API call failed for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, ex.Message);

                            // Check if this is a rate limiting related exception
                            if (ex.Message.Contains("429") || ex.Message.Contains("TooManyRequests") || ex.Message.Contains("rate limit"))
                            {
                                int retryAttempts = 0;
                                const int maxRetryAttempts = 3;
                                bool chunkProcessed = false;

                                while (!chunkProcessed && retryAttempts < maxRetryAttempts)
                                {
                                    retryAttempts++;
                                    _logger?.LogWarning("Rate limit related error for chunk {ChunkStart} to {ChunkEnd} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                        chunkStartStr, chunkEndStr, retryAttempts, maxRetryAttempts);

                                    // Get a new API key for rate limiting issues
                                    try
                                    {
                                        GCUtilities.GetGCAPIKey();
                                        GCApiKey = GCUtilities.GCApiKey;
                                    }
                                    catch (Exception keyEx)
                                    {
                                        _logger?.LogError(keyEx, "Failed to refresh API key for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, keyEx.Message);
                                    }

                                    // Implement exponential backoff with jitter
                                    int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                    Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));

                                    // Retry the API call
                                    try
                                    {
                                        chunkJsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);
                                        chunkProcessed = true;
                                        break;
                                    }
                                    catch (Exception retryEx)
                                    {
                                        if (retryAttempts >= maxRetryAttempts)
                                        {
                                            _logger?.LogError("Rate limiting exceeded retry limit for chunk {ChunkStart} to {ChunkEnd} after {Attempts} attempts - halting processing",
                                                chunkStartStr, chunkEndStr, maxRetryAttempts);
                                            throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts due to rate limiting");
                                        }
                                    }
                                }

                                if (!chunkProcessed)
                                {
                                    throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts");
                                }
                            }
                            else
                            {
                                // For other non-retryable errors, halt processing
                                throw;
                            }
                        }

                        // Check if the response is an error response and handle appropriately
                        if (chunkJsonString != null && (chunkJsonString.Contains("\"error\":") || chunkJsonString.Contains("\"status\":") || chunkJsonString.Contains("\"message\":")))
                        {
                            // Check if this is a rate limiting error that should be retried
                            if (chunkJsonString.Contains("429") || chunkJsonString.Contains("TooManyRequests") || chunkJsonString.Contains("rate limit"))
                            {
                                int retryAttempts = 0;
                                const int maxRetryAttempts = 3;
                                bool chunkProcessed = false;

                                while (!chunkProcessed && retryAttempts < maxRetryAttempts)
                                {
                                    retryAttempts++;
                                    _logger?.LogWarning("Rate limit error in response for chunk {ChunkStart} to {ChunkEnd} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                        chunkStartStr, chunkEndStr, retryAttempts, maxRetryAttempts);

                                    // Get a new API key for rate limiting issues
                                    try
                                    {
                                        GCUtilities.GetGCAPIKey();
                                        GCApiKey = GCUtilities.GCApiKey;
                                    }
                                    catch (Exception keyEx)
                                    {
                                        _logger?.LogError(keyEx, "Failed to refresh API key for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, keyEx.Message);
                                    }

                                    // Implement exponential backoff with jitter
                                    int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                    Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));

                                    // Retry the API call
                                    try
                                    {
                                        chunkJsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);

                                        // Check if the retry was successful (no error in response)
                                        if (chunkJsonString != null && !(chunkJsonString.Contains("\"error\":") || chunkJsonString.Contains("\"status\":") || chunkJsonString.Contains("\"message\":")))
                                        {
                                            chunkProcessed = true;
                                            break;
                                        }
                                    }
                                    catch (Exception retryEx)
                                    {
                                        _logger?.LogWarning(retryEx, "Retry attempt {Attempt} failed for chunk {ChunkStart} to {ChunkEnd}: {Message}", retryAttempts, chunkStartStr, chunkEndStr, retryEx.Message);

                                        if (retryAttempts >= maxRetryAttempts)
                                        {
                                            _logger?.LogError("Rate limiting exceeded retry limit for chunk {ChunkStart} to {ChunkEnd} after {Attempts} attempts - halting processing",
                                                chunkStartStr, chunkEndStr, maxRetryAttempts);
                                            throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts due to rate limiting");
                                        }
                                    }
                                }

                                if (!chunkProcessed)
                                {
                                    throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts due to rate limiting");
                                }
                            }
                            else if (chunkJsonString.Contains("400") || chunkJsonString.Contains("Bad Request"))
                            {
                                // For HTTP 400 errors, halt processing entirely
                                _logger?.LogError("HTTP 400 Bad Request error in response for chunk {ChunkStart} to {ChunkEnd} - halting processing: {Response}",
                                    chunkStartStr, chunkEndStr, chunkJsonString);
                                throw new Exception($"HTTP 400 Bad Request error for chunk {chunkStartStr} to {chunkEndStr}: {chunkJsonString}");
                            }
                            else
                            {
                                // For other errors, log and halt processing to ensure data integrity
                                _logger?.LogError("Error response received for chunk {ChunkStart} to {ChunkEnd} - halting processing to ensure data integrity: {Response}",
                                    chunkStartStr, chunkEndStr, chunkJsonString);
                                throw new Exception($"Error response for chunk {chunkStartStr} to {chunkEndStr}: {chunkJsonString}");
                            }
                        }

                        if (chunkJsonString != null && chunkJsonString.Length > 30)
                        {
                            Interactions.InteractionDataStruct chunkUserData = null;
                            try
                            {
                                chunkUserData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(chunkJsonString,
                                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                            }
                            catch (JsonException jsonEx)
                            {
                                _logger?.LogError(jsonEx, "JSON Deserialization Error for chunk {ChunkStart} to {ChunkEnd} in GetUserInteractionDataFromGC: {Message}", chunkStartStr, chunkEndStr, jsonEx.Message);
                                Console.WriteLine($"JSON Deserialization Error for chunk {chunkStartStr} to {chunkEndStr} in GetUserInteractionDataFromGC: {jsonEx.Message}");
                                throw; // Halt processing to ensure data integrity
                            }

                            if (chunkUserData != null && chunkUserData.results != null)
                            {
                                foreach (Interactions.Result chunkResults in chunkUserData.results)
                                {
                                    foreach (Interactions.Datum chunkResultsData in chunkResults.data)
                                    {
                                        string chunkTimeInterval = chunkResultsData.interval.Split('/')[0];
                                        if (!DateTime.TryParseExact(chunkTimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime chunkMaxUpdateDateTest))
                                        {
                                            Console.WriteLine($"Warning: Invalid TimeInterval format '{chunkTimeInterval}' in GetUserInteractionDataFromGC. Skipping record.");
                                            continue;
                                        }

                                        if (chunkMaxUpdateDateTest > UserInteractionLastUpdate)
                                            UserInteractionLastUpdate = chunkMaxUpdateDateTest;

                                        if (chunkResults.group.userId != null)
                                        {
                                            try
                                            {
                                                DataRow chunkDRNewRow = UserInteraction.NewRow();
                                                chunkDRNewRow["userId"] = chunkResults.group.userId;
                                                chunkDRNewRow["queueId"] = chunkResults.group.queueId ?? "NaN";
                                                chunkDRNewRow["mediaType"] = chunkResults.group.mediaType;
                                                chunkDRNewRow["direction"] = chunkResults.group.direction;

                                                // Use the wrapUpCode from the result
                                                string chunkRowWrapUp = chunkResults.group.wrapUpCode;
                                                if (chunkRowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                    chunkRowWrapUp = "00000000-0000-0000-0000-000000000000";

                                                chunkDRNewRow["wrapUpCode"] = chunkRowWrapUp;

                                            string chunkTempKeyid = $"{chunkResults.group.userId}|{chunkDRNewRow["queueId"]}|{chunkResults.group.mediaType}|{chunkRowWrapUp}|{chunkResults.group.direction}|{chunkTimeInterval}";
                                            chunkDRNewRow["keyId"] = $"{chunkResults.group.userId}|{UCAUtils.GetStableHashCode(chunkTempKeyid)}";

                                            DateTime chunkIntervalStart = new DateTime(
                                                chunkMaxUpdateDateTest.Ticks - (chunkMaxUpdateDateTest.Ticks % TimeSpan.TicksPerSecond),
                                                chunkMaxUpdateDateTest.Kind);
                                            chunkDRNewRow["startdate"] = chunkIntervalStart;
                                            chunkDRNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(chunkIntervalStart, AppTimeZone);

                                            foreach (DataColumn chunkDCTemp in UserInteraction.Columns)
                                            {
                                                if (chunkDCTemp.DataType == typeof(int) || chunkDCTemp.DataType == typeof(float) || chunkDCTemp.DataType == typeof(double))
                                                {
                                                    chunkDRNewRow[chunkDCTemp.ColumnName] = 0;
                                                }
                                            }

                                            foreach (Interactions.Metric chunkResultMetrics in chunkResultsData.metrics)
                                            {
                                                string chunkMetricName = chunkResultMetrics.metric.ToString();
                                                switch (chunkResultMetrics.metric)
                                                {
                                                    case "tAlert":
                                                    case "tAnswered":
                                                    case "tTalk":
                                                    case "tNotResponding":
                                                    case "tHeld":
                                                    case "tHeldComplete":
                                                    case "tAcw":
                                                    case "tContacting":
                                                    case "tDialing":
                                                    case "tHandle":
                                                    case "tTalkComplete":
                                                    case "tVoicemail":
                                                    case "tUserResponseTime":
                                                    case "tAgentResponseTime":
                                                        chunkDRNewRow[$"{chunkMetricName}Count"] = chunkResultMetrics.stats.count;
                                                        chunkDRNewRow[$"{chunkMetricName}TimeSum"] = Convert.ToInt32(Math.Round(chunkResultMetrics.stats.sum / 1000.00F, 2));
                                                        chunkDRNewRow[$"{chunkMetricName}TimeMax"] = Convert.ToInt32(Math.Round(chunkResultMetrics.stats.max / 1000.00F, 2));
                                                        chunkDRNewRow[$"{chunkMetricName}TimeMin"] = Convert.ToInt32(Math.Round(chunkResultMetrics.stats.min / 1000.00F, 2));
                                                        break;

                                                    case "nConsult":
                                                    case "nConsultTransferred":
                                                    case "nError":
                                                    case "nTransferred":
                                                    case "nBlindTransferred":
                                                    case "nOutbound":
                                                    case "nConnected":
                                                        chunkDRNewRow[chunkMetricName] = chunkResultMetrics.stats.count;
                                                        break;

                                                    default:
                                                        break;
                                                }
                                            }

                                            if (chunkResultsData.views != null)
                                            {
                                                foreach (Interactions.View chunkResultsView in chunkResultsData.views)
                                                {
                                                    string chunkMetricName = chunkResultsView.name;

                                                    chunkDRNewRow[$"{chunkMetricName}Count"] = chunkResultsView.stats.count;
                                                    chunkDRNewRow[$"{chunkMetricName}TimeSum"] = Convert.ToInt32(Math.Round(chunkResultsView.stats.sum / 1000.00F, 2));
                                                    chunkDRNewRow[$"{chunkMetricName}TimeMax"] = Convert.ToInt32(Math.Round(chunkResultsView.stats.max / 1000.00F, 2));
                                                    chunkDRNewRow[$"{chunkMetricName}TimeMin"] = Convert.ToInt32(Math.Round(chunkResultsView.stats.min / 1000.00F, 2));
                                                }
                                            }

                                            UserInteraction.Rows.Add(chunkDRNewRow);
                                            totalRowsProcessed++;

                                            // Log a summary every 50 rows
                                            if (totalRowsProcessed % 50 == 0)
                                            {
                                                _logger?.LogDebug("User interaction data processing: {RowCount} rows processed so far", totalRowsProcessed);
                                            }
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            // Duplicate row, ignore
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nError adding row in GetUserInteractionDataFromGC: {ex.Message}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Warning: Received an empty or invalid response from the API for chunk {chunkStartStr} to {chunkEndStr} in GetUserInteractionDataFromGC.");
                    }
                    } // End of chunk processing loop

                    Console.WriteLine($"\nCompleted processing all chunks. Total rows: {UserInteraction.Rows.Count}");
                }

                else
                {
                    // Process the full date range response
                    if (JsonString != null && JsonString.Length > 30)
                    {
                        Interactions.InteractionDataStruct UserData = null;
                        try
                        {
                            UserData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(JsonString,
                                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }
                        catch (JsonException jsonEx)
                        {
                            Console.WriteLine($"JSON Deserialization Error in GetUserInteractionDataFromGC: {jsonEx.Message}");
                            throw;
                        }

                        if (UserData != null && UserData.results != null)
                        {
                            foreach (Interactions.Result Results in UserData.results)
                            {
                                foreach (Interactions.Datum ResultsData in Results.data)
                                {
                                    string TimeInterval = ResultsData.interval.Split('/')[0];
                                    if (!DateTime.TryParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime MaxUpdateDateTest))
                                    {
                                        Console.WriteLine($"Warning: Invalid TimeInterval format '{TimeInterval}' in GetUserInteractionDataFromGC. Skipping record.");
                                        continue;
                                    }

                                    if (MaxUpdateDateTest > UserInteractionLastUpdate)
                                        UserInteractionLastUpdate = MaxUpdateDateTest;

                                    if (Results.group.userId != null)
                                    {
                                        try
                                        {
                                            DataRow DRNewRow = UserInteraction.NewRow();

                                            // Using standardized null value handling with ternary operator pattern
                                            DRNewRow["userId"] = Results.group.userId != null ? Results.group.userId : DBNull.Value;
                                            DRNewRow["queueId"] = Results.group.queueId != null ? Results.group.queueId : "NaN";
                                            DRNewRow["mediaType"] = Results.group.mediaType != null ? Results.group.mediaType : DBNull.Value;
                                            DRNewRow["direction"] = Results.group.direction != null ? Results.group.direction : DBNull.Value;

                                            string RowWrapUp = Results.group.wrapUpCode == "ININ-WRAP-UP-TIMEOUT"
                                                ? "00000000-0000-0000-0000-000000000000"
                                                : (Results.group.wrapUpCode != null ? Results.group.wrapUpCode : "");
                                            DRNewRow["wrapUpCode"] = RowWrapUp != null ? RowWrapUp : DBNull.Value;

                                            string TempKeyid = $"{Results.group.userId}|{DRNewRow["queueId"]}|{Results.group.mediaType}|{RowWrapUp}|{Results.group.direction}|{TimeInterval}";
                                            DRNewRow["keyId"] = TempKeyid != null ? $"{Results.group.userId}|{UCAUtils.GetStableHashCode(TempKeyid)}" : DBNull.Value;

                                            DateTime IntervalStart = new DateTime(
                                                MaxUpdateDateTest.Ticks - (MaxUpdateDateTest.Ticks % TimeSpan.TicksPerSecond),
                                                MaxUpdateDateTest.Kind);
                                            DRNewRow["startdate"] = IntervalStart;
                                            DRNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(IntervalStart, AppTimeZone);

                                            foreach (DataColumn DCTemp in UserInteraction.Columns)
                                            {
                                                if (DCTemp.DataType == typeof(int) || DCTemp.DataType == typeof(float) || DCTemp.DataType == typeof(double))
                                                {
                                                    DRNewRow[DCTemp.ColumnName] = 0;
                                                }
                                            }

                                            foreach (Interactions.Metric ResultMetrics in ResultsData.metrics)
                                            {
                                                string MetricName = ResultMetrics.metric.ToString();
                                                switch (ResultMetrics.metric)
                                                {
                                                    case "tAlert":
                                                    case "tAnswered":
                                                    case "tTalk":
                                                    case "tNotResponding":
                                                    case "tHeld":
                                                    case "tHeldComplete":
                                                    case "tAcw":
                                                    case "tContacting":
                                                    case "tDialing":
                                                    case "tHandle":
                                                    case "tTalkComplete":
                                                    case "tVoicemail":
                                                    case "tUserResponseTime":
                                                    case "tAgentResponseTime":
                                                        DRNewRow[$"{MetricName}Count"] = ResultMetrics.stats.count;
                                                        DRNewRow[$"{MetricName}TimeSum"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2));
                                                        DRNewRow[$"{MetricName}TimeMax"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.max / 1000.00F, 2));
                                                        DRNewRow[$"{MetricName}TimeMin"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.min / 1000.00F, 2));
                                                        break;

                                                    case "nConsult":
                                                    case "nConsultTransferred":
                                                    case "nError":
                                                    case "nTransferred":
                                                    case "nBlindTransferred":
                                                    case "nOutbound":
                                                    case "nConnected":
                                                        DRNewRow[MetricName] = ResultMetrics.stats.count;
                                                        break;

                                                    default:
                                                        // Console.WriteLine($"Warning: Unhandled metric '{ResultMetrics.metric}' in GetUserInteractionDataFromGC.");
                                                        break;
                                                }
                                            }

                                            if (ResultsData.views != null)
                                            {
                                                foreach (Interactions.View ResultsView in ResultsData.views)
                                                {
                                                    string MetricName = ResultsView.name;

                                                    DRNewRow[$"{MetricName}Count"] = ResultsView.stats.count;
                                                    DRNewRow[$"{MetricName}TimeSum"] = Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2));
                                                    DRNewRow[$"{MetricName}TimeMax"] = Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2));
                                                    DRNewRow[$"{MetricName}TimeMin"] = Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2));
                                                }
                                            }

                                            UserInteraction.Rows.Add(DRNewRow);
                                            totalRowsProcessed++;

                                            // Log a summary every 50 rows
                                            if (totalRowsProcessed % 50 == 0)
                                            {
                                                _logger?.LogDebug("User interaction data processing: {RowCount} rows processed so far", totalRowsProcessed);
                                            }
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            // Duplicate row, ignore
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nError adding row in GetUserInteractionDataFromGC: {ex.Message}");
                                        }
                                    }
                                }
                            }

                        }
                    }
                    else
                    {
                        Console.WriteLine("Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.");
                    }

                    Console.WriteLine($"\nCompleted processing full date range. Total rows: {UserInteraction.Rows.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "GetUserInteractionDataFromGC Error");
                throw;
            }

            // Add summary log at the end of the method
            int totalRows = UserInteraction?.Rows.Count ?? 0;
            int uniqueQueues = 0;
            int uniqueMediaTypes = 0;
            int uniqueDirections = 0;

            // Calculate summary statistics if we have data
            if (UserInteraction != null && UserInteraction.Rows.Count > 0)
            {
                // Count unique queues
                uniqueQueues = UserInteraction.AsEnumerable()
                    .Select(r => r["queueId"].ToString())
                    .Distinct()
                    .Count();

                // Count unique media types
                uniqueMediaTypes = UserInteraction.AsEnumerable()
                    .Select(r => r["mediaType"].ToString())
                    .Distinct()
                    .Count();

                // Count unique directions
                uniqueDirections = UserInteraction.AsEnumerable()
                    .Select(r => r["direction"].ToString())
                    .Distinct()
                    .Count();
            }

            _logger?.LogInformation($"\n[USER INTERACTION SUMMARY] API data processing complete for {StartDate} to {EndDate}.\n" +
                          $"Retrieved {totalRows} interaction records across {uniqueQueues} queues, {uniqueMediaTypes} media types, and {uniqueDirections} directions.\n" +
                          $"Data ready for database insertion.");

            return UserInteraction;
        }

        public DataTable GetUserDetailedPresenceFromGCJob(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                // Get presenceDetails table
                DataTable OrganisationPresence = DBUtil.GetSQLTableData("SELECT * FROM presenceDetails", "presenceDetails");
                DTPresenceTemp = DBUtil.CreateInMemTable("userPresenceDetailedData");
                Console.WriteLine("Created Temp Table for Detailed Presence Data.");

                Console.WriteLine($"Retrieving User Presence Detailed Data from {StartDate} to {EndDate}");
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                bool FoundData = false;
                string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\", \"order\": \"asc\" }";
                Console.WriteLine($"Job Request Body:\n{RequestBody}");

                HttpApiResponse apiResponse;
                try
                {
                    apiResponse = JsonActions.JsonReturnHttpResponse($"{URI}/api/v2/analytics/users/details/jobs", GCApiKey, RequestBody);
                }
                catch (HttpRequestException httpEx)
                {
                    _logger?.LogError(httpEx, "HTTP error in GetUserDetailedPresenceFromGCJob: {Message}", httpEx.Message);

                    // Check if this is a BadRequest (HTTP 400) which should halt processing
                    if (httpEx.Message.Contains("400") || httpEx.Message.Contains("Bad Request"))
                    {
                        _logger?.LogError("HTTP 400 Bad Request detected - halting processing");
                        throw; // Rethrow to halt processing
                    }

                    // For other HTTP errors, return null to indicate failure
                    return null;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "API Call Error in GetUserDetailedPresenceFromGCJob: {Message}", ex.Message);
                    throw;
                }

                // Validate response using proper HTTP status code detection
                if (string.IsNullOrWhiteSpace(apiResponse.Content))
                {
                    _logger?.LogWarning("Presence Detailed Data: Empty response received - Exiting.");
                    return null;
                }

                // Handle different HTTP status codes appropriately
                if (!apiResponse.IsSuccess && !apiResponse.IsAccepted)
                {
                    _logger?.LogError("API Error in GetUserDetailedPresenceFromGCJob: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                        apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                    // Check for specific error types that should halt processing
                    if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                    {
                        _logger?.LogError("Critical API error detected (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                        throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode}: {apiResponse.Content}");
                    }

                    // For other errors, return null to indicate failure
                    return null;
                }

                DetInt.ReportJob JobID = null;
                try
                {
                    JobID = JsonConvert.DeserializeObject<DetInt.ReportJob>(apiResponse.Content,
                                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    // Include problematic JSON in the error message for debugging
                    string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                    _logger?.LogError(jsonEx, "JSON Deserialization Error in GetUserDetailedPresenceFromGCJob. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                        apiResponse.StatusCode, jsonPreview);
                    throw;
                }

                Console.WriteLine("Presence Detailed Data: Job Queued. Waiting 15 seconds...");
                System.Threading.Thread.Sleep(15000);

                while (!FoundData)
                {
                    System.Threading.Thread.Sleep(3000);
                    string JobStatusUrl = $"{URI}/api/v2/analytics/users/details/jobs/{JobID.jobId}";
                    HttpApiResponse jobStatusResponse;
                    try
                    {
                        jobStatusResponse = JsonActions.JsonReturnHttpResponseGet(JobStatusUrl, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while checking job status in GetUserDetailedPresenceFromGCJob: {ex.Message}");
                        throw;
                    }

                    // Validate response using proper HTTP status code detection
                    if (string.IsNullOrWhiteSpace(jobStatusResponse.Content))
                    {
                        _logger?.LogWarning("Empty job status response received - retrying");
                        continue;
                    }

                    // Handle different HTTP status codes appropriately
                    if (!jobStatusResponse.IsSuccess)
                    {
                        _logger?.LogError("API Error while checking job status: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            jobStatusResponse.StatusCode, jobStatusResponse.StatusDescription, jobStatusResponse.Content);

                        // Check for specific error types that should halt processing
                        if (jobStatusResponse.StatusCode == 400 || jobStatusResponse.StatusCode == 403)
                        {
                            _logger?.LogError("Critical API error detected while checking job status (HTTP {StatusCode}) - halting processing", jobStatusResponse.StatusCode);
                            throw new HttpRequestException($"API returned HTTP {jobStatusResponse.StatusCode} while checking job status: {jobStatusResponse.Content}");
                        }

                        // For other errors, continue to retry
                        _logger?.LogWarning("Retrying job status check due to HTTP {StatusCode} error", jobStatusResponse.StatusCode);
                        continue;
                    }

                    DetInt.ReportJobStatus JobIdStatus = null;
                    try
                    {
                        JobIdStatus = JsonConvert.DeserializeObject<DetInt.ReportJobStatus>(jobStatusResponse.Content,
                                                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        // Include problematic JSON in the error message for debugging
                        string jsonPreview = jobStatusResponse.Content.Length > 200 ? jobStatusResponse.Content.Substring(0, 200) + "..." : jobStatusResponse.Content;
                        _logger?.LogError(jsonEx, "JSON Deserialization Error while checking job status in GetUserDetailedPresenceFromGCJob. Problematic JSON: {JsonPreview}", jsonPreview);
                        throw;
                    }

                    if (jobStatusResponse.Content.Contains("Rate limit exceeded the maximum"))
                    {
                        Console.WriteLine("Presence Detailed Data: Rate Limited. Sleeping for 30 seconds.");
                        System.Threading.Thread.Sleep(30000);
                        continue;
                    }

                    if (JobIdStatus.state == "FULFILLED")
                        FoundData = true;
                    else if (JobIdStatus.state == "FAILED")
                    {
                        Console.WriteLine("\nPresence Detailed Data: Job Failed - Exiting.");
                        return null;
                    }
                    else
                        Console.WriteLine($"\nPresence Detailed Data: Job ID: {JobID.jobId} Status: {JobIdStatus.state} - Pausing.");
                }

                Console.WriteLine($"\nPresence Detailed Data: Job ID: {JobID.jobId} Status: FULFILLED");

                bool FirstTime = true;
                bool RepeatDownload = true;
                string CursorString = string.Empty;
                string LastCursor = string.Empty;

                do
                {
                    if (FirstTime)
                    {
                        CursorString = "";
                    }
                    else
                    {
                        CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor);
                        Console.WriteLine($"\nRetrieving Next Page: {HttpUtility.UrlEncode(LastCursor)}\n");
                    }

                    string ResultsUrl = $"{URI}/api/v2/analytics/users/details/jobs/{JobID.jobId}/results{CursorString}";
                    HttpApiResponse resultsResponse;
                    try
                    {
                        resultsResponse = JsonActions.JsonReturnHttpResponseGet(ResultsUrl, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while retrieving job results in GetUserDetailedPresenceFromGCJob: {ex.Message}");
                        throw;
                    }

                    // Validate response using proper HTTP status code detection
                    if (string.IsNullOrWhiteSpace(resultsResponse.Content))
                    {
                        _logger?.LogWarning("Empty results response received - breaking download loop");
                        RepeatDownload = false;
                        break;
                    }

                    // Handle different HTTP status codes appropriately
                    if (!resultsResponse.IsSuccess)
                    {
                        _logger?.LogError("API Error while retrieving job results: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            resultsResponse.StatusCode, resultsResponse.StatusDescription, resultsResponse.Content);

                        // Check for specific error types that should halt processing
                        if (resultsResponse.StatusCode == 400 || resultsResponse.StatusCode == 403)
                        {
                            _logger?.LogError("Critical API error detected while retrieving job results (HTTP {StatusCode}) - halting processing", resultsResponse.StatusCode);
                            throw new HttpRequestException($"API returned HTTP {resultsResponse.StatusCode} while retrieving job results: {resultsResponse.Content}");
                        }

                        // For other errors, break out of the download loop
                        _logger?.LogWarning("Breaking download loop due to HTTP {StatusCode} error", resultsResponse.StatusCode);
                        RepeatDownload = false;
                        break;
                    }

                    PresDefJob.UserPresenceDetailJob UserPresenceData = null;
                    try
                    {
                        UserPresenceData = JsonConvert.DeserializeObject<PresDefJob.UserPresenceDetailJob>(resultsResponse.Content,
                                               new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        // Include problematic JSON in the error message for debugging
                        string jsonPreview = resultsResponse.Content.Length > 200 ? resultsResponse.Content.Substring(0, 200) + "..." : resultsResponse.Content;
                        _logger?.LogError(jsonEx, "JSON Deserialization Error while retrieving job results in GetUserDetailedPresenceFromGCJob. Problematic JSON: {JsonPreview}", jsonPreview);
                        throw;
                    }

                    FirstTime = false;

                    if (UserPresenceData.userDetails.Count() > 0)
                    {
                        foreach (PresDefJob.Userdetail UserPresenceDetail in UserPresenceData.userDetails)
                        {
                            Console.Write($"US:{UserPresenceDetail.userId}");

                            string UserID = UserPresenceDetail.userId;

                            if (UserPresenceData.cursor != null)
                            {
                                LastCursor = UserPresenceData.cursor;
                                RepeatDownload = true;
                            }
                            else
                            {
                                RepeatDownload = false;
                            }

                            if (UserPresenceDetail.routingStatus != null)
                            {
                                foreach (PresDefJob.Routingstatus RoutingStatus in UserPresenceDetail.routingStatus)
                                {
                                    Console.Write("R");

                                    // RoutingStatus.endTime comes from API deserialization, use ToUtcSafe to avoid double conversion
                                    if (RoutingStatus.endTime.ToUtcSafe() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("ER:");
                                    else
                                    {
                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();
                                            RowRS["keyid"] = $"{UserID}|{RoutingStatus.routingStatus}|{RoutingStatus.startTime}|{RoutingStatus.endTime}";
                                            RowRS["userid"] = UserID;
                                            RowRS["routingstatus"] = RoutingStatus.routingStatus;

                                            RoutingStatus.startTime = new DateTime(
                                                 RoutingStatus.startTime.Ticks - (RoutingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                                 RoutingStatus.startTime.Kind
                                             );

                                            RoutingStatus.endTime = new DateTime(
                                                RoutingStatus.endTime.Ticks - (RoutingStatus.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                RoutingStatus.endTime.Kind
                                            );

                                            // RoutingStatus times come from API deserialization, use ToUtcSafe to avoid double conversion
                                            RowRS["starttime"] = RoutingStatus.startTime.ToUtcSafe();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.startTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["endtime"] = RoutingStatus.endTime.ToUtcSafe();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.endTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["timeinstate"] = (RoutingStatus.endTime - RoutingStatus.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nRouting Detail Error in GetUserDetailedPresenceFromGCJob: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            if (UserPresenceDetail.primaryPresence != null)
                            {
                                foreach (PresDefJob.Primarypresence PresenceDetails in UserPresenceDetail.primaryPresence)
                                {
                                    // PresenceDetails.endTime comes from API deserialization, use ToUtcSafe to avoid double conversion
                                    if (PresenceDetails.endTime.ToUtcSafe() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("EP:");
                                    else
                                    {
                                        Console.Write("P");
                                        string OrgPresenceDesc = "UNKNOWN";
                                        DataRow[] RowOrgPresence = OrganisationPresence.Select("id = '" + PresenceDetails.organizationPresenceId + "'");

                                        if (RowOrgPresence.Length > 0)
                                            OrgPresenceDesc = RowOrgPresence[0]["orgpresence"].ToString();

                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();

                                            if (PresenceDetails.systemPresence == "TRAINING" && OrgPresenceDesc == "P&C")
                                            {
                                                OrgPresenceDesc = "P_AND_C";
                                            }

                                            RowRS["userid"] = UserID;
                                            RowRS["systempresence"] = PresenceDetails.systemPresence;
                                            RowRS["keyid"] = $"{UserID}|{PresenceDetails.systemPresence}|{OrgPresenceDesc}|{PresenceDetails.startTime}|{PresenceDetails.endTime}";
                                            RowRS["orgpresence"] = OrgPresenceDesc;

                                            PresenceDetails.startTime = new DateTime(
                                              PresenceDetails.startTime.Ticks - (PresenceDetails.startTime.Ticks % TimeSpan.TicksPerSecond),
                                              PresenceDetails.startTime.Kind
                                            );

                                            PresenceDetails.endTime = new DateTime(
                                                PresenceDetails.endTime.Ticks - (PresenceDetails.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                PresenceDetails.endTime.Kind
                                            );

                                            // PresenceDetails times come from API deserialization, use ToUtcSafe to avoid double conversion
                                            RowRS["starttime"] = PresenceDetails.startTime.ToUtcSafe();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.startTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["endtime"] = PresenceDetails.endTime.ToUtcSafe();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.endTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["timeinstate"] = (PresenceDetails.endTime - PresenceDetails.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nPresence Detail Error in GetUserDetailedPresenceFromGCJob: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            Console.WriteLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("\nPresence Detailed Data: No Data Returned - No More Pages.");
                        RepeatDownload = false;
                    }

                } while (RepeatDownload);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGCJob Error: {ex.Message}");
                throw;
            }

            // Add summary log at the end of the method
            Console.WriteLine($"\n[USER DETAILED PRESENCE JOB SUMMARY] API data processing complete for {StartDate} to {EndDate}.\n" +
                          $"Retrieved {DTPresenceTemp?.Rows.Count ?? 0} detailed presence records.\n" +
                          $"Data ready for database insertion.");

            return DTPresenceTemp;
        }

        public DataTable GetUserDetailedPresenceFromGCQuery(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                // Get presenceDetails table
                DataTable OrganisationPresence = DBUtil.GetSQLTableData("SELECT * FROM presenceDetails", "presenceDetails");
                DTPresenceTemp = DBUtil.CreateInMemTable("userPresenceDetailedData");
                Console.WriteLine("Created Temp Table for Detailed Presence Data.");

                Console.WriteLine($"Retrieving User Presence Detailed Data from {StartDate} to {EndDate}");
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                int PageNumber = 0;
                bool GetPage = true;

                while (GetPage)
                {
                    PageNumber++;
                    string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\", \"paging\": { \"pageSize\": 100, \"pageNumber\": " + PageNumber + " }}";
                    Console.WriteLine($"Requesting Page {PageNumber} with Body:\n{RequestBody}");

                    string JsonString = string.Empty;
                    try
                    {
                        JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/details/query", GCApiKey, RequestBody);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while retrieving detailed presence data in GetUserDetailedPresenceFromGCQuery: {ex.Message}");
                        throw;
                    }

                    if (JsonString.Length > 30)
                    {
                        Console.Write("PG:");
                        PresDef.DetailedPresenceInfo PresInfo = null;
                        try
                        {
                            PresInfo = JsonConvert.DeserializeObject<PresDef.DetailedPresenceInfo>(JsonString,
                                                   new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }
                        catch (JsonException jsonEx)
                        {
                            Console.WriteLine($"JSON Deserialization Error in GetUserDetailedPresenceFromGCQuery: {jsonEx.Message}");
                            throw;
                        }

                        foreach (PresDef.Userdetail UserPresence in PresInfo.userDetails)
                        {
                            string UserID = UserPresence.userId;
                            Console.Write($"US:{UserID}");

                            if (UserPresence.routingStatus != null)
                            {
                                foreach (PresDef.Routingstatus RoutingStatus in UserPresence.routingStatus)
                                {
                                    Console.Write("R");

                                    // RoutingStatus.endTime comes from API deserialization, use ToUtcSafe to avoid double conversion
                                    if (RoutingStatus.endTime.ToUtcSafe() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("ER:");
                                    else
                                    {
                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();
                                            RowRS["keyid"] = $"{UserID}|{RoutingStatus.routingStatus}|{RoutingStatus.startTime}|{RoutingStatus.endTime}";
                                            RowRS["userid"] = UserID;
                                            RowRS["routingstatus"] = RoutingStatus.routingStatus;

                                            RoutingStatus.startTime = new DateTime(
                                                 RoutingStatus.startTime.Ticks - (RoutingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                                 RoutingStatus.startTime.Kind
                                             );

                                            RoutingStatus.endTime = new DateTime(
                                                RoutingStatus.endTime.Ticks - (RoutingStatus.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                RoutingStatus.endTime.Kind
                                            );

                                            // RoutingStatus times come from API deserialization, use ToUtcSafe to avoid double conversion
                                            RowRS["starttime"] = RoutingStatus.startTime.ToUtcSafe();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.startTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["endtime"] = RoutingStatus.endTime.ToUtcSafe();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.endTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["timeinstate"] = (RoutingStatus.endTime - RoutingStatus.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nRouting Detail Error in GetUserDetailedPresenceFromGCQuery: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            if (UserPresence.primaryPresence != null)
                            {
                                foreach (PresDef.Primarypresence PresenceDetails in UserPresence.primaryPresence)
                                {
                                    // PresenceDetails.endTime comes from API deserialization, use ToUtcSafe to avoid double conversion
                                    if (PresenceDetails.endTime.ToUtcSafe() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("EP:");
                                    else
                                    {
                                        Console.Write("P");
                                        string OrgPresenceDesc = "UNKNOWN";
                                        DataRow[] RowOrgPresence = OrganisationPresence.Select("id = '" + PresenceDetails.organizationPresenceId + "'");

                                        if (RowOrgPresence.Length > 0)
                                            OrgPresenceDesc = RowOrgPresence[0]["orgpresence"].ToString();

                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();

                                            if (PresenceDetails.systemPresence == "TRAINING" && OrgPresenceDesc == "P&C")
                                            {
                                                OrgPresenceDesc = "P_AND_C";
                                            }

                                            RowRS["userid"] = UserID;
                                            RowRS["systempresence"] = PresenceDetails.systemPresence;
                                            RowRS["keyid"] = $"{UserID}|{PresenceDetails.systemPresence}|{OrgPresenceDesc}|{PresenceDetails.startTime}|{PresenceDetails.endTime}";
                                            RowRS["orgpresence"] = OrgPresenceDesc;

                                            PresenceDetails.startTime = new DateTime(
                                              PresenceDetails.startTime.Ticks - (PresenceDetails.startTime.Ticks % TimeSpan.TicksPerSecond),
                                              PresenceDetails.startTime.Kind
                                            );

                                            PresenceDetails.endTime = new DateTime(
                                                PresenceDetails.endTime.Ticks - (PresenceDetails.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                PresenceDetails.endTime.Kind
                                            );

                                            // PresenceDetails times come from API deserialization, use ToUtcSafe to avoid double conversion
                                            RowRS["starttime"] = PresenceDetails.startTime.ToUtcSafe();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.startTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["endtime"] = PresenceDetails.endTime.ToUtcSafe();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.endTime.ToUtcSafe(), AppTimeZone);
                                            RowRS["timeinstate"] = (PresenceDetails.endTime - PresenceDetails.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nPresence Detail Error in GetUserDetailedPresenceFromGCQuery: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            Console.WriteLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("\nPresence Detailed Data: No Data Returned - No More Pages.");
                        GetPage = false;
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGCQuery Error: {ex.Message}");
                throw;
            }

            // Add summary log at the end of the method
            Console.WriteLine($"\n[USER DETAILED PRESENCE QUERY SUMMARY] API data processing complete for {StartDate} to {EndDate}.\n" +
                          $"Retrieved {DTPresenceTemp?.Rows.Count ?? 0} detailed presence records.\n" +
                          $"Data ready for database insertion.");

            return DTPresenceTemp;
        }

        public DataTable GetUserDetailedPresenceFromGC(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                string JsonString = string.Empty;

                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/details/jobs/availability", GCApiKey);

                    // Validate response before proceeding - fail-fast for non-JSON responses
                    if (!string.IsNullOrWhiteSpace(JsonString))
                    {
                        string trimmedResponse = JsonString.TrimStart();
                        if (!trimmedResponse.StartsWith('{') && !trimmedResponse.StartsWith('['))
                        {
                            _logger?.LogError("API returned non-JSON response in GetUserDetailedPresenceFromGCQuery. Response preview: {ResponsePreview}",
                                JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                            throw new HttpRequestException($"API returned non-JSON content: {JsonString.Substring(0, Math.Min(JsonString.Length, 200))}");
                        }
                    }

                    _logger?.LogDebug("Availability JSON Response: {JsonString}", JsonString);
                }
                catch (HttpRequestException httpEx)
                {
                    _logger?.LogError(httpEx, "HTTP error in GetUserDetailedPresenceFromGCQuery: {Message}", httpEx.Message);

                    // Check if this is a BadRequest (HTTP 400) which should halt processing
                    if (httpEx.Message.Contains("400") || httpEx.Message.Contains("Bad Request"))
                    {
                        _logger?.LogError("HTTP 400 Bad Request detected - halting processing");
                        throw; // Rethrow to halt processing
                    }

                    // For other HTTP errors, rethrow to indicate failure
                    throw;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "API Call Error in GetUserDetailedPresenceFromGCQuery: {Message}", ex.Message);
                    throw;
                }

                JobDateLimit DateMax = null;
                try
                {
                    DateMax = JsonConvert.DeserializeObject<JobDateLimit>(JsonString,
                                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    _logger?.LogError(jsonEx, "JSON Deserialization Error in GetUserDetailedPresenceFromGCQuery. Response content: {JsonContent}", JsonString);
                    throw;
                }

                DateTime DteStartCheck = DateTime.Parse(StartDate);
                DateTime DteEndCheck = DateTime.Parse(EndDate);
                DateTime DteJobCheck = DateMax.dataAvailabilityDate;

                Console.WriteLine($"\nStart Date: {DteStartCheck} | End Date: {DteEndCheck} | Data Availability Date: {DteJobCheck}");

                if (DteStartCheck > DteJobCheck)
                {
                    Console.WriteLine("Presence Detailed Data: Performing Query Only.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCQuery(StartDate, EndDate);
                }
                else if (DteEndCheck > DteJobCheck)
                {
                    Console.WriteLine("Presence Detailed Data: Combining Job and Query.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCJob(StartDate, DteJobCheck.ToString("yyyy-MM-ddTHH:00:00.000Z"));

                    DataTable QueryData = GetUserDetailedPresenceFromGCQuery(DteJobCheck.AddHours(-2).ToString("yyyy-MM-ddTHH:00:00.000Z"), EndDate);
                    if (QueryData != null)
                    {
                        foreach (DataRow DRTempRow in QueryData.Rows)
                        {
                            try
                            {
                                DTPresenceTemp.ImportRow(DRTempRow);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"\nError importing row in GetUserDetailedPresenceFromGC: {ex.Message}");
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Presence Detailed Data: Performing Job Only.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCJob(StartDate, EndDate);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGC Error: {ex.Message}");
                throw;
            }

            // Add summary log at the end of the method
            Console.WriteLine($"\n[USER DETAILED PRESENCE COMBINED SUMMARY] API data processing complete for {StartDate} to {EndDate}.\n" +
                          $"Retrieved {DTPresenceTemp?.Rows.Count ?? 0} detailed presence records.\n" +
                          $"Data ready for database insertion.");

            return DTPresenceTemp;
        }
    }
}
