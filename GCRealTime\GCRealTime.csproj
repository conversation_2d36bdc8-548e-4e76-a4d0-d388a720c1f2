﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <NoWarn>CS8632</NoWarn>
    <!--
    CS8632  The annotation for nullable reference types should only be used in code within a '#nullable' annotations context
    -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ChilkatDnCore" Version="9.5.0.91" />
    <!--
    TODO: Remove or check references below when updating Chilkat library to see if NU1605 error change
    -->
    <PackageReference Include="System.Diagnostics.Tracing" Version="4.3.*" />
    <PackageReference Include="System.Net.NameResolution" Version="4.3.*" />
    <PackageReference Include="System.Net.Primitives" Version="4.3.*" />
    <PackageReference Include="System.Threading.ThreadPool" Version="4.3.*" />
    <!--
    NU1605: Detected package downgrade: System.Diagnostics.Tracing from 4.3.0 to 4.1.0.
            Reference the package directly from the project to select a different version.
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> NETStandard.Library 1.6.0 ->
            System.Net.Primitives 4.0.11 -> runtime.win.System.Net.Primitives 4.3.0 ->
            System.Diagnostics.Tracing (>= 4.3.0)
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> NETStandard.Library 1.6.0 ->
            System.Diagnostics.Tracing (>= 4.1.0)
    NU1605: Detected package downgrade: System.Net.NameResolution from 4.3.0 to 4.0.0.
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> NETStandard.Library 1.6.0 ->
            System.Net.Sockets 4.1.0 -> runtime.win.System.Net.Sockets 4.3.0 -> System.Net.NameResolution (>= 4.3.0)
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> System.Net.NameResolution (>= 4.0.0)
    NU1605: Detected package downgrade: System.Net.Primitives from 4.3.0 to 4.0.11.
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> NETStandard.Library 1.6.0 ->
            System.Net.Sockets 4.1.0 -> runtime.win.System.Net.Sockets 4.3.0 -> System.Net.Primitives (>= 4.3.0)
    NU1605: ChilkatDnCore 9.5.0.91 -> Microsoft.NETCore.App 1.0.5 -> NETStandard.Library 1.6.0 ->
            System.Net.Primitives (>= 4.0.11)
    -->

    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Include="System.IO.FileSystem" Version="4.3.0" />

    <!-- 
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      -->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\StandardUtils\StandardUtils.csproj" />
    <ProjectReference Include="..\GenesysCloudUtils\GenesysCloudUtils.csproj" />
  </ItemGroup>

</Project>
