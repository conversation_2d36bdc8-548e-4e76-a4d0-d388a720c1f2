CREATE TABLE IF NOT EXISTS detailedinteractiondata (
    keyid varchar(255) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    conversationminmos numeric(20, 2),
    conversationminrfactor numeric(20, 2),
    externaltag varchar(50),
    originaldirection varchar(50),
    participantid varchar(50),
    participantname varchar(255),
    purpose varchar(50),
    mediatype varchar(50),
    ani varchar(400),
    dnis varchar(400),
    sessiondnis varchar(400),
    edgeid varchar(50),
    gencode varchar(20),
    remotedisplayable varchar(255),
    segmentstartdate timestamp without time zone,
    segmentstartdateltc timestamp without time zone,
    segmentenddate timestamp without time zone,
    segmentenddateltc timestamp without time zone,
    segmenttime numeric(20, 2),
    convtosegmentstarttime numeric(20, 2),
    convtosegmentendtime numeric(20, 2),
    segmenttype varchar(50),
    conference number,
    disconnectiontype varchar(50),
    wrapupcode varchar(255),
    wrapupnote varchar,
    recordingexists number,
    sessionprovider varchar(50),
    flowid varchar(50),
    flowname varchar(255),
    flowversion numeric(20, 2),
    flowtype varchar(50),
    exitreason varchar(100),
    entryreason varchar(500),
    entrytype varchar(50),
    transfertype varchar(50),
    transfertargetname varchar(255),
    queueid varchar(50),
    userid varchar(50),
    issuedcallback number,
    nflow integer,
    tivr numeric(20, 2),
    tflow numeric(20, 2),
    tflowdisconnect numeric(20, 2),
    tflowexit numeric(20, 2),
    tflowout numeric(20, 2),
    tacd numeric(20, 2),
    tacw numeric(20, 2),
    talert numeric(20, 2),
    tanswered numeric(20, 2),
    tconnected numeric(20, 2),
    ttalk numeric(20, 2),
    ttalkcomplete numeric(20, 2),
    thandle numeric(20, 2),
    tcontacting numeric(20, 2),
    tdialing numeric(20, 2),
    tnotresponding numeric(20, 2),
    tabandon numeric(20, 2),
    theld numeric(20, 2),
    theldcomplete numeric(20, 2),
    tvoicemail numeric(20, 2),
    tmonitoring numeric(20, 2),
    tmonitoringcomplete numeric(20, 2),
    tshortabandon numeric(20, 2),
    tagentresponsetime numeric(20, 2),
    tActiveCallback numeric(20, 2),
    tActiveCallbackComplete numeric(20, 2),
    noffered integer,
    nconnected integer,
    nconsult integer,
    nconsulttransferred integer,
    ntransferred integer,
    nblindtransferred integer,
    nerror integer,
    noutbound integer,
    nstatetransitionerror integer,
    noversla integer,
    noutboundattempted integer,
    noutboundconnected integer,
    sessiondirection varchar(50),
    segdestinationconversationid varchar(50),
    tfirstdial numeric(20, 2),
    tfirstconnect numeric(20, 2),
    tuserresponsetime numeric(20, 2),
    nflowoutcome integer,
    tflowoutcome numeric(20, 2),
    nflowoutcomefailed integer,
    nbotinteractions integer,
    tPark numeric(20, 2),
    tParkComplete numeric(20, 2),
    omessagecount integer,
    omessagesegmentcount integer,
    omessageturn integer,
    peer varchar(100),
    divisionid varchar(100) NOT NULL,
    divisionid2 varchar(100),
    divisionid3 varchar(100),
    updated timestamp without time zone,
    CONSTRAINT detailedinteractiondata_pkey PRIMARY KEY (keyid, divisionid, conversationstartdate)
);

ALTER TABLE detailedinteractiondata
ALTER COLUMN segmenttime TYPE NUMERIC(20,2);
GO

ALTER TABLE detailedinteractiondata
ALTER COLUMN convtosegmentstarttime TYPE NUMERIC(20,2);
GO

ALTER TABLE detailedinteractiondata
ALTER COLUMN convtosegmentendtime TYPE NUMERIC(20,2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists tfirstconnect numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  tfirstdial numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  tconnected numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  tmonitoringcomplete numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists wrapupnote VARCHAR;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists tActiveCallback numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  tActiveCallbackComplete numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  nbotinteractions integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists tPark numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists  tParkComplete numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists nCobrowseSessions integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists omessagecount integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists omessagesegmentcount integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists omessageturn integer;
GO

ALTER TABLE detailedinteractiondata
ALTER COLUMN wrapupnote VARCHAR;
GO

-- Add missing Session-level fields
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists sessionid varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists protocolcallid varchar(100);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists remotenamedisplayable varchar(255);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists callbackusername varchar(255);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists callbacknumbers varchar;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists scriptid varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists skipenabled number;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists timeoutseconds integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists flowouttype varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists roomid varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists callbackscheduledtime timestamp without time zone;
GO

-- Add missing Flow-level fields
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists transfertargetaddress varchar(255);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists startinglanguage varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists endinglanguage varchar(50);
GO

-- Add missing Segment-level fields
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists requestedroutingskillids varchar;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists sipresponsecodes varchar;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists q850responsecodes varchar;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists errorcode varchar(100);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists requestedlanguageid varchar(50);
GO

-- Add missing Participant-level fields
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists externalcontactid varchar(50);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists externalorganizationid varchar(50);
GO

-- Add missing Media Endpoint Statistics fields
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists codecs varchar;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists minmos numeric(10, 4);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists minrfactor numeric(10, 4);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists maxlatencyms integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists receivedpackets integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists discardedpackets integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists overrunpackets integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists invalidpackets integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists duplicatepackets integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists nOutboundAbandoned integer;
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists nStateTransitionError integer;
GO

-- Add missing Coaching metrics
ALTER TABLE detailedinteractiondata
ADD column IF NOT exists tCoaching numeric(20, 2);
GO

ALTER TABLE detailedinteractiondata
ADD column IF NOT exists tCoachingComplete numeric(20, 2);
GO
