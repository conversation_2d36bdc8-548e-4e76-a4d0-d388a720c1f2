IF dbo.csg_table_exists('participantAttributesDynamic') = 0
CREATE TABLE [participantAttributesDynamic](
    [keyid] [nvarchar](50) NOT NULL,
    [conversationid] [nvarchar](50),
    [conversationstartdate] [datetime],
    [conversationstartdateltc] [datetime],
    [conversationenddate] [datetime],
    [conversationenddateltc] [datetime],
    [updated] [datetime],
    CONSTRAINT [PK_participantAttributesDynamic] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('participantAttributesDynamic_conversationenddate', 'participantAttributesDynamic') = 0
CREATE INDEX [participantAttributesDynamic_conversationenddate] ON [participantAttributesDynamic]([conversationenddate]);
IF dbo.csg_index_exists('participantAttributesDynamic_conversationenddateltc', 'participantAttributesDynamic') = 0
CREATE INDEX [participantAttributesDynamic_conversationenddateltc] ON [participantAttributesDynamic]([conversationenddateltc]);
IF dbo.csg_index_exists('participantAttributesDynamic_conversationstartdate', 'participantAttributesDynamic') = 0
CREATE INDEX [participantAttributesDynamic_conversationstartdate] ON [participantAttributesDynamic]([conversationstartdate]);
IF dbo.csg_index_exists('participantAttributesDynamic_conversationstartdateltc', 'participantAttributesDynamic') = 0
CREATE INDEX [participantAttributesDynamic_conversationstartdateltc] ON [participantAttributesDynamic]([conversationstartdateltc]);

-- Fix participant attributes data corruption from threading race conditions
IF dbo.csg_table_exists('participantAttributesDynamic') = 1
BEGIN
    UPDATE participantAttributesDynamic
    SET conversationid = keyid
    WHERE conversationid != keyid;
END