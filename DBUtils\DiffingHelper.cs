using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using CSG.Adapter.Configuration;

namespace DBUtils
{
    /// <summary>
    /// Helper class for diffing data between external sources and the database
    /// </summary>
    public class DiffingHelper
    {
        private readonly ILogger? _logger;

        public DiffingHelper(ILogger? logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Diffs data from an external source against existing data in the database (synchronous version)
        /// </summary>
        /// <param name="tableName">The name of the table to diff against</param>
        /// <param name="sourceData">The data table containing source data</param>
        /// <param name="dbAdapter">The database adapter</param>
        /// <param name="lookbackDays">Number of days to look back for the date condition</param>
        /// <param name="dateColumnName">The name of the date column to use in the where condition</param>
        /// <returns>A data table containing only the rows that need to be inserted or updated</returns>
        public DataTable DiffData(
            string tableName,
            DataTable sourceData,
            DBUtils dbAdapter,
            int lookbackDays = 7,
            string dateColumnName = "updated")
        {
            // Create a batching diff processor for better performance with large datasets
            var diffProcessor = new BatchingDiffProcessor(_logger);

            // Call the async version synchronously
            return DiffDataAsync(
                tableName,
                sourceData,
                dbAdapter,
                diffProcessor,
                lookbackDays,
                dateColumnName,
                sourceData.Columns.Count > 0 ? sourceData.Columns[0].ColumnName : "id"
            ).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Diffs data from an external source against existing data in the database using a specific where condition
        /// </summary>
        /// <param name="tableName">The name of the table to diff against</param>
        /// <param name="whereCondition">The where condition to use for filtering database records</param>
        /// <param name="sourceData">The data table containing source data</param>
        /// <param name="dbAdapter">The database adapter</param>
        /// <param name="keyColumnName">The name of the key column to use for diffing</param>
        /// <returns>A data table containing only the rows that need to be inserted or updated</returns>
        public DataTable DiffDataWithCondition(
            string tableName,
            string whereCondition,
            DataTable sourceData,
            DBUtils dbAdapter,
            string keyColumnName = "keyid")
        {
            // Create a batching diff processor for better performance with large datasets
            var diffProcessor = new BatchingDiffProcessor(_logger);

            // Call the async version synchronously with the provided where condition
            return diffProcessor.DiffDataFromDBwithBatchesStreamingAsync(
                tableName,
                whereCondition,
                sourceData,
                dbAdapter,
                keyColumnName
            ).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Diffs data from an external source against existing data in the database
        /// </summary>
        /// <param name="tableName">The name of the table to diff against</param>
        /// <param name="sourceData">The data table containing source data</param>
        /// <param name="dbAdapter">The database adapter</param>
        /// <param name="diffProcessor">The object that performs the actual diffing operation</param>
        /// <param name="lookbackDays">Number of days to look back for the date condition (default: 7)</param>
        /// <param name="dateColumnName">The name of the date column to use in the where condition (default: "updated")</param>
        /// <param name="keyColumnName">The name of the key column to use for diffing (default: "keyid")</param>
        /// <returns>A data table containing only the rows that need to be inserted or updated</returns>
        public async Task<DataTable> DiffDataAsync(
            string tableName,
            DataTable sourceData,
            DBUtils dbAdapter,
            IDiffProcessor diffProcessor,
            int lookbackDays = 7,
            string dateColumnName = "updated",
            string keyColumnName = "keyid")
        {
            if (sourceData == null || sourceData.Rows.Count == 0)
            {
                _logger?.LogInformation("No data to diff for {TableName}", tableName);
                return sourceData ?? new DataTable();
            }

            // Create the where condition for the diffing query
            DateTime startDate = DateTime.UtcNow.AddDays(-lookbackDays);
            DateTime endDate = DateTime.UtcNow;

            string whereCondition = $@"
                {dateColumnName} >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                AND {dateColumnName} <= '{endDate:yyyy-MM-dd HH:mm:ss}'
            ";

            // Use the provided diffing processor
            DataTable diffedData = await diffProcessor.DiffDataFromDBwithBatchesStreamingAsync(
                tableName,
                whereCondition,
                sourceData,
                dbAdapter,
                keyColumnName
            );

            return diffedData;
        }

        /// <summary>
        /// Processes data with diffing and writes it to the database
        /// </summary>
        /// <param name="tableName">The name of the table to write to</param>
        /// <param name="sourceData">The data table containing source data</param>
        /// <param name="dbAdapter">The database adapter</param>
        /// <param name="diffProcessor">The object that performs the actual diffing operation</param>
        /// <param name="lastSuccessDateUpdater">Function to update the last success date</param>
        /// <param name="syncType">The sync type for updating the last success date</param>
        /// <param name="updateDate">The date to use for updating the last success date</param>
        /// <param name="lookbackDays">Number of days to look back for the date condition (default: 7)</param>
        /// <param name="dateColumnName">The name of the date column to use in the where condition (default: "updated")</param>
        /// <param name="keyColumnName">The name of the key column to use for diffing (default: "keyid")</param>
        /// <returns>True if the operation was successful, false otherwise</returns>
        public async Task<bool> ProcessWithDiffingAsync(
            string tableName,
            DataTable sourceData,
            DBUtils dbAdapter,
            IDiffProcessor diffProcessor,
            Func<DateTime, string, bool> lastSuccessDateUpdater,
            string syncType,
            DateTime updateDate,
            int lookbackDays = 7,
            string dateColumnName = "updated",
            string keyColumnName = "keyid")
        {
            bool successful = false;

            if (sourceData == null || sourceData.Rows.Count == 0)
            {
                _logger?.LogInformation("No data to process for {TableName}", tableName);
                // Still update the last sync date even if no rows were found
                successful = lastSuccessDateUpdater(updateDate, syncType);
                return successful;
            }

            // Diff the data
            DataTable diffedData = await DiffDataAsync(
                tableName,
                sourceData,
                dbAdapter,
                diffProcessor,
                lookbackDays,
                dateColumnName,
                keyColumnName
            );

            // If there are no rows to write, we're done
            if (diffedData.Rows.Count == 0)
            {
                _logger?.LogInformation("No new or updated data to write for {TableName}. Updating last sync date to {Date}.", tableName, updateDate);
                successful = lastSuccessDateUpdater(updateDate, syncType);
                return successful;
            }

            // Write the diffed data
            successful = dbAdapter.WriteSQLDataBulk(diffedData, tableName);

            if (successful)
            {
                _logger?.LogInformation("Data saved for {TableName}. Updating last sync date to {Date}.", tableName, updateDate);
                successful = lastSuccessDateUpdater(updateDate, syncType);
            }
            else
            {
                _logger?.LogWarning("Failed to write data for {TableName}; last sync date not updated.", tableName);
            }

            return successful;
        }
    }

    /// <summary>
    /// Interface for objects that can perform diffing operations
    /// </summary>
    public interface IDiffProcessor
    {
        /// <summary>
        /// Diffs data from the database with batches streaming
        /// </summary>
        Task<DataTable> DiffDataFromDBwithBatchesStreamingAsync(
            string tableName,
            string whereCondition,
            DataTable sourceData,
            DBUtils dbAdapter,
            string keyColumnName);
    }

    /// <summary>
    /// A simple implementation of IDiffProcessor that performs basic diffing operations
    /// </summary>
    public class SimpleDiffProcessor : IDiffProcessor
    {
        /// <summary>
        /// Diffs data from the database with batches streaming
        /// </summary>
        public async Task<DataTable> DiffDataFromDBwithBatchesStreamingAsync(
            string tableName,
            string whereCondition,
            DataTable sourceData,
            DBUtils dbAdapter,
            string keyColumnName)
        {
            // Get existing data from the database
            string query = $"SELECT * FROM {tableName} WHERE {whereCondition}";
            DataTable existingData = dbAdapter.GetSQLTableData(query, tableName);

            // Create a new table with the same schema as the source data
            DataTable diffedData = sourceData.Clone();

            // Create a dictionary of existing rows for faster lookup
            var existingRows = new Dictionary<string, DataRow>();
            foreach (DataRow row in existingData.Rows)
            {
                string key = row[keyColumnName]?.ToString() ?? "";
                if (!string.IsNullOrEmpty(key) && !existingRows.ContainsKey(key))
                {
                    existingRows[key] = row;
                }
            }

            int newRows = 0;
            int updatedRows = 0;

            // Process each row in the source data
            foreach (DataRow sourceRow in sourceData.Rows)
            {
                string key = sourceRow[keyColumnName]?.ToString() ?? "";
                if (string.IsNullOrEmpty(key))
                {
                    continue;
                }

                // If the key doesn't exist in the database, add it as a new row
                if (!existingRows.ContainsKey(key))
                {
                    diffedData.ImportRow(sourceRow);
                    newRows++;
                    continue;
                }

                // Compare the source row with the existing row
                DataRow existingRow = existingRows[key];
                bool needsUpdate = false;

                // Compare each column value
                foreach (DataColumn column in sourceData.Columns)
                {
                    // Skip the key column
                    if (column.ColumnName == keyColumnName)
                    {
                        continue;
                    }

                    // Get the values to compare
                    object sourceValue = sourceRow[column.ColumnName];

                    // If the column doesn't exist in the existing data, skip it
                    if (!existingData.Columns.Contains(column.ColumnName))
                    {
                        needsUpdate = true;
                        break;
                    }

                    object existingValue = existingRow[column.ColumnName];

                    // Handle null values
                    if (sourceValue == DBNull.Value && existingValue == DBNull.Value)
                    {
                        continue;
                    }

                    if (sourceValue == DBNull.Value || existingValue == DBNull.Value)
                    {
                        needsUpdate = true;
                        break;
                    }

                    // For numeric values, compare with a small tolerance
                    if (column.DataType == typeof(decimal) ||
                        column.DataType == typeof(double) ||
                        column.DataType == typeof(float))
                    {
                        double sourceDouble = Convert.ToDouble(sourceValue);
                        double existingDouble = Convert.ToDouble(existingValue);

                        if (Math.Abs(sourceDouble - existingDouble) > 0.001)
                        {
                            needsUpdate = true;
                            break;
                        }
                    }
                    // For DateTime values, compare only the date part
                    else if (column.DataType == typeof(DateTime))
                    {
                        DateTime sourceDate = (DateTime)sourceValue;
                        DateTime existingDate = (DateTime)existingValue;

                        if (Math.Abs((sourceDate - existingDate).TotalSeconds) > 1)
                        {
                            needsUpdate = true;
                            break;
                        }
                    }
                    // For all other types, use Equals
                    else if (!sourceValue.Equals(existingValue))
                    {
                        needsUpdate = true;
                        break;
                    }
                }

                // If the row needs to be updated, add it to the diffed data
                if (needsUpdate)
                {
                    diffedData.ImportRow(sourceRow);
                    updatedRows++;
                }
            }

            return diffedData;
        }
    }

    /// <summary>
    /// An advanced implementation of IDiffProcessor that processes data in batches for better performance with large datasets
    /// </summary>
    public class BatchingDiffProcessor : IDiffProcessor
    {
        private readonly ILogger? _logger;
        // Reduced batch size from 100,000 to 10,000 for better performance with large datasets
        private const int DefaultBatchSize = 10000;
        // Add a progress reporting interval
        private const int ProgressReportInterval = 5000;
        // Add a timeout for the diffing operation (10 minutes)
        private const int DiffingTimeoutMinutes = 10;
        // Flag to indicate if we're working with a partitioned table
        private bool IsPartitionedTable = false;

        public BatchingDiffProcessor(ILogger? logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Diffs data from the database with batches streaming for better performance with large datasets
        /// </summary>
        public async Task<DataTable> DiffDataFromDBwithBatchesStreamingAsync(
            string tableName,
            string whereCondition,
            DataTable sourceData,
            DBUtils dbAdapter,
            string keyColumnName)
        {
            if (sourceData == null || sourceData.Rows.Count == 0)
            {
                _logger?.LogInformation("No data to diff for {TableName}", tableName);
                return sourceData ?? new DataTable();
            }

            _logger?.LogInformation("Diffing {Count} rows from source for {TableName}", sourceData.Rows.Count, tableName);

            // Set a timeout for the diffing operation
            DateTime startTime = DateTime.UtcNow;
            DateTime timeoutTime = startTime.AddMinutes(DiffingTimeoutMinutes);

            // Check if this is a partitioned table (naming pattern suggests partitioning)
            bool isPartitionedTable = tableName.Contains("_p20");

            // For partitioned tables, we need to be more careful with the primary key
            // The primary key might be composite (e.g., keyid, divisionid, conversationstartdate)
            string[] keyColumns = { keyColumnName };

            // For detailedinteractiondata table, we know it has a composite primary key
            if (tableName.Contains("detailedinteractiondata"))
            {
                keyColumns = new[] { "keyid", "divisionid", "conversationstartdate" };
                _logger?.LogDebug("Using composite primary key for partitioned table {TableName}: {Keys}",
                    tableName, string.Join(", ", keyColumns));
            }

            // Build an in-memory lookup (dictionary) of the source rows keyed by a composite key if needed
            _logger?.LogDebug("Building source data lookup dictionary for {TableName}", tableName);
            var gcRowsLookup = new Dictionary<string, DataRow>();

            foreach (DataRow row in sourceData.AsEnumerable())
            {
                // For composite keys, concatenate the values
                string compositeKey = BuildCompositeKey(row, keyColumns);

                // Skip rows with null key values
                if (string.IsNullOrEmpty(compositeKey))
                    continue;

                // Add to lookup, handling potential duplicates
                if (!gcRowsLookup.ContainsKey(compositeKey))
                {
                    gcRowsLookup.Add(compositeKey, row);
                }
                else
                {
                    _logger?.LogWarning("Duplicate key found in source data for {TableName}: {Key}", tableName, compositeKey);
                }
            }

            _logger?.LogDebug("Completed building lookup dictionary with {Count} entries for {TableName}",
                gcRowsLookup.Count, tableName);

            int batchSize = DefaultBatchSize;
            int offset = 0;
            bool moreRecords = true;
            string finalTableName = tableName;
            int totalRowsProcessed = 0;
            int totalBatchesProcessed = 0;

            // Adjust table name for PostgreSQL
            if (dbAdapter.DBType == DatabaseType.PostgreSQL)
                finalTableName = $"{dbAdapter.PostgresSchema}.{tableName.ToLower()}";

            // Process database records in batches
            while (moreRecords && gcRowsLookup.Count > 0)
            {
                // Check for timeout
                if (DateTime.UtcNow > timeoutTime)
                {
                    _logger?.LogWarning("Diffing operation for {TableName} timed out after {Minutes} minutes. Processed {Rows} rows in {Batches} batches. Returning current results.",
                        tableName, DiffingTimeoutMinutes, totalRowsProcessed, totalBatchesProcessed);
                    break;
                }

                totalBatchesProcessed++;
                string batchQuery = $@"
                    SELECT * FROM {finalTableName}
                    WHERE {whereCondition}
                    OFFSET {offset} ROWS
                    FETCH NEXT {batchSize} ROWS ONLY
                ";

                // For databases that don't support OFFSET/FETCH syntax
                if (dbAdapter.DBType == DatabaseType.MySQL)
                {
                    batchQuery = $@"
                        SELECT * FROM {finalTableName}
                        WHERE {whereCondition}
                        LIMIT {batchSize} OFFSET {offset}
                    ";
                }

                // Log batch query execution at Debug level
                _logger?.LogDebug("Executing batch query {BatchNum} for {TableName}: Offset={Offset}, Batch={BatchSize}",
                    totalBatchesProcessed, tableName, offset, batchSize);

                DataTable dtBatch = dbAdapter.GetSQLTableData(batchQuery, tableName);

                if (dtBatch == null || dtBatch.Rows.Count == 0)
                {
                    _logger?.LogInformation("No more records found in database for {TableName} after {Batches} batches",
                        tableName, totalBatchesProcessed);
                    moreRecords = false;
                    continue;
                }

                // Sort the batch data in memory by the key column to ensure consistent ordering
                // This replaces the SQL ORDER BY clause for better database performance
                if (dtBatch.Columns.Contains(keyColumnName))
                {
                    DataView sortedView = dtBatch.DefaultView;
                    sortedView.Sort = $"{keyColumnName} ASC";
                    dtBatch = sortedView.ToTable();
                }

                int batchRowCount = dtBatch.Rows.Count;
                totalRowsProcessed += batchRowCount;
                _logger?.LogDebug("Retrieved {Count} rows from database for {TableName} (batch {BatchNum}, offset {Offset})",
                    batchRowCount, tableName, totalBatchesProcessed, offset);

                int rowsProcessedInBatch = 0;
                // Process each row in the current batch
                foreach (DataRow dbRow in dtBatch.Rows)
                {
                    rowsProcessedInBatch++;

                    // Report progress periodically within the batch, but only for very large batches
                    // to reduce log verbosity while still providing feedback for long-running operations
                    if (batchRowCount > 20000 && rowsProcessedInBatch % (ProgressReportInterval * 2) == 0)
                    {
                        _logger?.LogDebug("Processing batch {BatchNum} for {TableName}: {Processed}/{Total} rows processed",
                            totalBatchesProcessed, tableName, rowsProcessedInBatch, batchRowCount);
                    }

                    // For composite keys, concatenate the values
                    string key = BuildCompositeKey(dbRow, keyColumns);

                    // If we have a matching source row, compare them
                    if (gcRowsLookup.TryGetValue(key, out DataRow gcRow))
                    {
                        bool needsUpdate = false;

                        // Compare each column value
                        foreach (DataColumn column in sourceData.Columns)
                        {
                            // Skip the key column and updated column
                            if (column.ColumnName.Equals(keyColumnName, StringComparison.OrdinalIgnoreCase) ||
                                column.ColumnName.Equals("updated", StringComparison.OrdinalIgnoreCase))
                            {
                                continue;
                            }

                            // If the column doesn't exist in the DB table, mark for update
                            if (!dbRow.Table.Columns.Contains(column.ColumnName))
                            {
                                needsUpdate = true;
                                break;
                            }

                            // Get the values to compare
                            object gcValue = gcRow[column.ColumnName];
                            object dbValue = dbRow[column.ColumnName];

                            // Handle null values
                            if (gcValue == DBNull.Value && dbValue == DBNull.Value)
                                continue;

                            if (gcValue == DBNull.Value || dbValue == DBNull.Value)
                            {
                                needsUpdate = true;
                                break;
                            }

                            // For numeric values, compare with a small tolerance
                            if (column.DataType == typeof(decimal) ||
                                column.DataType == typeof(double) ||
                                column.DataType == typeof(float))
                            {
                                double gcDouble = Convert.ToDouble(gcValue);
                                double dbDouble = Convert.ToDouble(dbValue);

                                if (Math.Abs(gcDouble - dbDouble) > 0.001)
                                {
                                    needsUpdate = true;
                                    break;
                                }
                            }
                            else if (!gcValue.Equals(dbValue))
                            {
                                needsUpdate = true;
                                break;
                            }
                        }

                        // If no update is needed, remove this source row from our lookup
                        if (!needsUpdate)
                        {
                            gcRowsLookup.Remove(key);
                        }
                    }
                    // If no matching source row is found, we don't need to do anything
                    // (we're only interested in rows that need to be inserted or updated)
                }

                // Log batch completion with summary information
                _logger?.LogDebug("Completed processing batch {BatchNum} for {TableName}: {ProcessedRows} rows processed, {RemainingRows} rows remain to be processed",
                    totalBatchesProcessed, tableName, batchRowCount, gcRowsLookup.Count);

                // Dispose the current batch before moving on
                dtBatch.Dispose();
                offset += batchSize;

                // Add a small delay to allow other processes to run and prevent CPU saturation
                if (totalBatchesProcessed % 5 == 0)
                {
                    await Task.Delay(100);
                }
            }

            // Build a new DataTable with only the source rows that need to be inserted or updated
            DataTable finalDiffTable = sourceData.Clone();
            int rowsToWrite = 0;

            _logger?.LogDebug("Building final diff table for {TableName} with {Count} rows",
                tableName, gcRowsLookup.Count);

            foreach (var kvp in gcRowsLookup)
            {
                finalDiffTable.ImportRow(kvp.Value);
                rowsToWrite++;

                // Report progress for large result sets
                if (rowsToWrite % 10000 == 0)
                {
                    _logger?.LogDebug("Building final diff table: {RowsProcessed}/{TotalRows} rows imported",
                        rowsToWrite, gcRowsLookup.Count);
                }
            }

            TimeSpan processingTime = DateTime.UtcNow - startTime;
            _logger?.LogInformation("Diffing completed for {TableName} in {Minutes:F2} minutes. {Count} rows need to be written to the database.",
                tableName, processingTime.TotalMinutes, finalDiffTable.Rows.Count);

            return finalDiffTable;
        }

        /// <summary>
        /// Builds a composite key from multiple columns in a DataRow
        /// </summary>
        /// <param name="row">The DataRow containing the key values</param>
        /// <param name="keyColumns">Array of column names that make up the composite key</param>
        /// <returns>A string representation of the composite key</returns>
        private string BuildCompositeKey(DataRow row, string[] keyColumns)
        {
            if (row == null || keyColumns == null || keyColumns.Length == 0)
                return string.Empty;

            var keyParts = new List<string>();

            foreach (string columnName in keyColumns)
            {
                // Skip if column doesn't exist
                if (!row.Table.Columns.Contains(columnName))
                {
                    _logger?.LogWarning("Column {ColumnName} not found in table when building composite key", columnName);
                    continue;
                }

                // Handle null values
                if (row[columnName] == DBNull.Value)
                {
                    _logger?.LogWarning("Null value found for key column {ColumnName}", columnName);
                    return string.Empty; // Skip rows with null key values
                }

                keyParts.Add(row[columnName].ToString());
            }

            // Join the key parts with a separator that's unlikely to appear in the data
            return string.Join("||", keyParts);
        }
    }
}
