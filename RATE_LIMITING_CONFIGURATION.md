# Rate Limiting Configuration Guide

## Overview
The Genesys Adapter now supports configurable rate limiting for API requests, particularly for participant attributes processing. This allows you to customize the rate limiting behavior based on your Genesys Cloud organization's specific API limits and requirements.

## Configuration Location
Rate limiting settings are configured in the `Preferences.RateLimiting` section of your `appsettings.json` file or through environment variables.

## Configuration Parameters

### `ParticipantAttributesMaxRequestsPerMinute`
- **Type**: Integer
- **Default**: 1950
- **Description**: Maximum number of participant attributes API requests allowed per minute
- **Calculation**: This is 65% of Genesys Cloud's default limit of 3000 requests per minute
- **Range**: 1 - 3000 (should not exceed your organization's actual API limit)

### `WindowDurationSeconds`
- **Type**: Integer  
- **Default**: 60
- **Description**: Duration of the rate limiting window in seconds
- **Range**: 30 - 300 (30 seconds to 5 minutes)
- **Note**: Most Genesys Cloud rate limits are per-minute, so 60 seconds is recommended

### `TokenRefreshInterval`
- **Type**: Integer
- **Default**: 275
- **Description**: Number of API requests after which to refresh the OAuth token
- **Range**: 100 - 1000
- **Purpose**: Prevents token-related rate limiting by getting fresh tokens regularly

### `SafetyMarginPercentage`
- **Type**: Integer
- **Default**: 65
- **Description**: Percentage of the actual API limit to use as a safety margin
- **Range**: 50 - 95
- **Purpose**: Provides buffer to avoid hitting hard limits and allows for other API usage

## Configuration Examples

### Default Configuration (Recommended)
```json
{
  "Preferences": {
    "RateLimiting": {
      "ParticipantAttributesMaxRequestsPerMinute": 1950,
      "WindowDurationSeconds": 60,
      "TokenRefreshInterval": 275,
      "SafetyMarginPercentage": 65
    }
  }
}
```

### Conservative Configuration (For Shared Organizations)
```json
{
  "Preferences": {
    "RateLimiting": {
      "ParticipantAttributesMaxRequestsPerMinute": 1500,
      "WindowDurationSeconds": 60,
      "TokenRefreshInterval": 200,
      "SafetyMarginPercentage": 50
    }
  }
}
```

### Aggressive Configuration (For Dedicated Organizations)
```json
{
  "Preferences": {
    "RateLimiting": {
      "ParticipantAttributesMaxRequestsPerMinute": 2700,
      "WindowDurationSeconds": 60,
      "TokenRefreshInterval": 400,
      "SafetyMarginPercentage": 90
    }
  }
}
```

## Environment Variable Configuration
You can also configure rate limiting using environment variables with the `CSG_` prefix:

```bash
CSG_Preferences__RateLimiting__ParticipantAttributesMaxRequestsPerMinute=1950
CSG_Preferences__RateLimiting__WindowDurationSeconds=60
CSG_Preferences__RateLimiting__TokenRefreshInterval=275
CSG_Preferences__RateLimiting__SafetyMarginPercentage=65
```

## Command Line Configuration
Rate limiting can be configured via command line arguments:

```bash
./GenesysAdapter \
  --Preferences:RateLimiting:ParticipantAttributesMaxRequestsPerMinute=1950 \
  --Preferences:RateLimiting:WindowDurationSeconds=60 \
  --Preferences:RateLimiting:TokenRefreshInterval=275 \
  --Preferences:RateLimiting:SafetyMarginPercentage=65
```

## Monitoring and Logging
When rate limiting is active, you'll see log messages like:

```
Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
ParticipantAttributes:RateLimit: Hit rate limit (1950/min). Processed 3893 conversations. Pausing 11.5s.
ParticipantAttributes:RateLimit: Pause complete. Processed 3893 conversations. Resuming.
```

## Troubleshooting

### Still Getting Rate Limited?
1. **Reduce `ParticipantAttributesMaxRequestsPerMinute`**: Try 1500 or 1200
2. **Increase `WindowDurationSeconds`**: Try 90 or 120 seconds
3. **Reduce `TokenRefreshInterval`**: Try 200 or 150 requests
4. **Lower `SafetyMarginPercentage`**: Try 50% or 55%

### Processing Too Slowly?
1. **Check your organization's actual API limits** with Genesys Cloud support
2. **Increase `ParticipantAttributesMaxRequestsPerMinute`** if you have higher limits
3. **Increase `SafetyMarginPercentage`** if you're not hitting limits
4. **Reduce `TokenRefreshInterval`** if token refresh is causing delays

### Rate Limit Errors in Logs?
- Look for "Rate Limiting Failures" in the logs
- If >10% of requests are failing due to rate limits, reduce your configured limits
- Check if other applications are using the same Genesys Cloud organization

## Best Practices

1. **Start Conservative**: Begin with default settings and adjust based on monitoring
2. **Monitor Logs**: Watch for rate limiting messages and adjust accordingly
3. **Consider Peak Times**: Your organization may have different limits during peak usage
4. **Test Changes**: Test configuration changes during low-traffic periods
5. **Document Changes**: Keep track of what settings work best for your environment

## Integration with Existing Features
- Rate limiting works alongside existing retry logic
- Compatible with backfill operations
- Integrates with the existing logging and telemetry systems
- Works with all supported database types (PostgreSQL, MSSQL, Snowflake)

## Version Compatibility
- Available in Genesys Adapter v3.48.0 and later
- Backward compatible - if not configured, uses default values
- No breaking changes to existing configurations
