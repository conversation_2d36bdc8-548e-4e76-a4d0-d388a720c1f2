    "Job": "factdata",
    "Database": {
    "Type":                 "postgresql",
        "Address":          "customerscience-development-01.cluster-cwcvdxxyb7ck.ap-southeast-2.rds.amazonaws.com",
        "Port":             5432,
        "Name":             "kwtest",
        "User":             "ucatech",
        "Password":         "enc:v2:fLxyKLrPLxjNuKhDuY+wXwLbtBIkLBrw8VMkvYaQE0pnwDSyQgMTsnlqgktjRYqCtaiwxJWIQFTuubgSu+P6iA==",
        "Schema":           "contactcentredb",
        "ConnectOptions":   "Include Error Detail=True"
    },
    "GenesysApi": {
        "ClientId":         "a7bc9436-4a1f-4573-9e43-40cdd0499f0d",
        "ClientSecret":     "enc:v2:mzmos0rlIR2Zctz0ib4uUWBxqoqnikQKX/yOt2wLOpnjLS+D3TO3vor24karASnpvpGUsvnNHHjzEAW1ywcWtl/7u0stXXUhlmge3aAiUhQ=",
        "Endpoint":         "https://api.mypurecloud.com.au"
    },
    "LogLevel":             "Debug",
    "Preferences": {
        "Backfill":                         false,
        "SharedDatabase":                   true,
        "FactDataJobs":                     ["ScheduleDetails"],
        "OffsetMonths":                     1,
        "TimeZone":                         "Australia/Sydney",
        "MaxSyncSpan":                      "1.00:00:00",
        "LookBackSpan":                     "1.00:00:00",
        "Granularity":                      "PT30M",
        "BlockParticipantAttributes":   ["(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)"],
        "RenameParticipantAttributeNames": [
            {"Find": "^\\d{2}:\\d{2}:\\d{2}[\\s-_]", "Replace": ""}
        ],
        "Telemetry":                        false,
        "Permissions":                      {
                                                "Update":       false,
                                                "ForcedUpdate": false
                                            }
    }
}