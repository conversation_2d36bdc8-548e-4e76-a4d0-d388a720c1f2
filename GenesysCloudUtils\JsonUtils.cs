﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Handles rate limiting for Genesys Cloud API requests
    /// </summary>
    public class RateLimitHandler
    {
        private readonly ILogger? _logger;
        private readonly GCUtils _gcUtils;
        private string _lastResponseContent = string.Empty;
        private const int MAX_RETRY_ATTEMPTS = 10; // Increased from 5 to handle analytics API rate limiting
        private const int DEFAULT_RETRY_SECONDS = 5;
        private const int MAX_WAIT_TIME_SECONDS = 120; // Increased from 60 to handle analytics API delays

        // Counters for tracking processing
        private static int _totalProcessed = 0;
        private static int _totalErrors = 0;
        private static int _totalRateLimits = 0;
        private static readonly object _counterLock = new object();

        public RateLimitHandler(ILogger? logger, GCUtils gcUtils)
        {
            _logger = logger;
            _gcUtils = gcUtils;
        }

        /// <summary>
        /// Determines if a rate limit response should trigger a token refresh
        /// Enhanced to recognize all token-related rate limits including platform.api variants
        /// </summary>
        public bool IsTokenRateLimit(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            // Check for all token-related rate limit patterns
            var tokenPatterns = new[]
            {
                "client.credentials.token",
                "token.rate.limit",
                "client.credentials.token.rate.per.minute",
                "platform.api.client.credentials.token",
                "platform.api.client.credentials.token.rate.per.minute"
            };

            foreach (var pattern in tokenPatterns)
            {
                if (responseContent.Contains(pattern))
                {
                    _logger?.LogDebug("Rate limit: Detected token-related rate limit pattern: {Pattern}", pattern);
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Extracts rate limit information from response content
        /// Enhanced to handle multiple rate limit response formats from Genesys Cloud
        /// </summary>
        public (int retryAfterSeconds, string limitKey, string limitNamespace, int? limitValue) ExtractRateLimitInfo(string responseContent)
        {
            int retryAfterSeconds = DEFAULT_RETRY_SECONDS;
            string limitKey = string.Empty;
            string limitNamespace = string.Empty;
            int? limitValue = null;

            try
            {
                // Extract retry-after value from multiple formats
                if (responseContent.Contains("Retry the request in"))
                {
                    var match = Regex.Match(responseContent, @"Retry the request in \[(\d+)\] seconds");
                    if (match.Success && match.Groups.Count > 1)
                    {
                        retryAfterSeconds = int.Parse(match.Groups[1].Value);
                    }
                }
                else if (responseContent.Contains("retry after:"))
                {
                    // Handle format: "retry after: 19"
                    var match = Regex.Match(responseContent, @"retry after:\s*(\d+)");
                    if (match.Success && match.Groups.Count > 1)
                    {
                        retryAfterSeconds = int.Parse(match.Groups[1].Value);
                    }
                }

                // Extract limit information from JSON structure
                if (responseContent.Contains("\"limit\""))
                {
                    var keyMatch = Regex.Match(responseContent, @"""key"":""([^""]+)""");
                    if (keyMatch.Success && keyMatch.Groups.Count > 1)
                    {
                        limitKey = keyMatch.Groups[1].Value;
                    }

                    var namespaceMatch = Regex.Match(responseContent, @"""namespace"":""([^""]+)""");
                    if (namespaceMatch.Success && namespaceMatch.Groups.Count > 1)
                    {
                        limitNamespace = namespaceMatch.Groups[1].Value;
                    }

                    var valueMatch = Regex.Match(responseContent, @"""value"":(\d+)");
                    if (valueMatch.Success && valueMatch.Groups.Count > 1)
                    {
                        limitValue = int.Parse(valueMatch.Groups[1].Value);
                    }
                }

                // Try to extract rate limit key directly from known patterns if JSON parsing fails
                if (string.IsNullOrEmpty(limitKey))
                {
                    // Look for specific rate limit patterns in the response content
                    var patterns = new[]
                    {
                        @"client\.credentials\.token\.rate\.per\.minute",
                        @"client\.credentials\.token",
                        @"token\.rate\.limit",
                        @"platform\.api\.rate\.limit",
                        @"analytics\..*\.rate\.limit",
                        @"conversations\..*\.rate\.limit"
                    };

                    foreach (var pattern in patterns)
                    {
                        if (Regex.IsMatch(responseContent, pattern, RegexOptions.IgnoreCase))
                        {
                            var match = Regex.Match(responseContent, pattern, RegexOptions.IgnoreCase);
                            if (match.Success)
                            {
                                limitKey = match.Value;
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to extract information from rate limit response content");
            }

            return (retryAfterSeconds, limitKey, limitNamespace, limitValue);
        }

        /// <summary>
        /// Refreshes the API key when a token-related rate limit is encountered
        /// Only refreshes for specific token-related rate limits, not organizational limits
        /// </summary>
        public string? RefreshApiKey(string responseContent)
        {
            try
            {
                bool isTokenRateLimit = IsTokenRateLimit(responseContent);

                if (isTokenRateLimit)
                {
                    // Rate limit counter is already incremented in HandleRateLimit method
                    // No need to increment here to avoid double counting

                    // Try to initialize GCUtils if not already done
                    if (string.IsNullOrEmpty(_gcUtils.GCApiKey))
                    {
                        try
                        {
                            _gcUtils.Initialize();
                        }
                        catch (Exception initEx)
                        {
                            // Suppressed: Only log at debug level to reduce noise
                            _logger?.LogDebug(initEx, "Rate limit: Failed to initialize GCUtils for API key refresh");
                        }
                    }

                    // Attempt to get a new API key from configuration
                    // Note: With reverted authentication, this may fail due to rate limiting on the auth endpoint itself
                    try
                    {
                        bool success = _gcUtils.GetGCAPIKey();

                        if (success)
                        {
                            string newApiKey = _gcUtils.GCApiKey;
                            _logger?.LogInformation("Rate limit: Successfully obtained new API key via token refresh");
                            return newApiKey;
                        }
                        else
                        {
                            _logger?.LogWarning("Rate limit: Failed to refresh API key - GetGCAPIKey returned false");
                        }
                    }
                    catch (Exception authEx)
                    {
                        // With reverted authentication, rate limiting on auth endpoint will cause exceptions
                        // This is expected behavior, so we log it as a warning for visibility
                        _logger?.LogWarning(authEx, "Rate limit: Unable to refresh API key due to authentication rate limiting - this may be expected with current auth configuration");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                lock (_counterLock)
                {
                    _totalErrors++;
                }
                // Suppressed: Only log at debug level to reduce noise
                _logger?.LogDebug(ex, "Rate limit: Exception occurred while attempting to refresh API key");
                return null;
            }
        }

        /// <summary>
        /// Refreshes the API key using provided credentials when configuration-based refresh fails
        /// </summary>
        public string? RefreshApiKey(string responseContent, string userId, string password, string uri)
        {
            try
            {
                bool isTokenRateLimit = IsTokenRateLimit(responseContent);

                if (isTokenRateLimit)
                {
                    // Use provided credentials to get a new API key
                    bool success = _gcUtils.GetGCAPIKey(userId, password, uri);

                    if (success)
                    {
                        string newApiKey = _gcUtils.GCApiKey;
                        _logger?.LogDebug("Successfully refreshed API key using provided credentials for token-related rate limit");
                        return newApiKey;
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to refresh API key using provided credentials - GetGCAPIKey returned false");
                    }
                }
                else
                {
                    _logger?.LogDebug("Not refreshing API key - rate limit is not token-related (may be organizational limit)");
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception occurred while attempting to refresh API key with provided credentials after rate limit response");
                return null;
            }
        }

        /// <summary>
        /// Handles rate limiting with enhanced logic for token refresh and wait strategies
        /// When a 429 occurs:
        /// 1. If it's a token limit, request a new token
        /// 2. If unable to get the conversation after getting a new token, honor the rate limit wait
        /// 3. In all other scenarios, perform a wait
        /// </summary>
        public (string apiKey, bool shouldRetry, bool tokenRefreshAttempted) HandleRateLimit(
            string retryAfterHeaderValue,
            string responseContent,
            int attempt,
            string apiKey)
        {
            _lastResponseContent = responseContent;
            bool shouldRetry = attempt < MAX_RETRY_ATTEMPTS;
            bool tokenRefreshAttempted = false;

            // Update rate limit counter - this method is called when a rate limit is encountered
            lock (_counterLock)
            {
                _totalRateLimits++;
            }

            // Log rate limit details
            var (retryAfterSeconds, limitKey, limitNamespace, limitValue) = ExtractRateLimitInfo(responseContent);

            // Use retry-after header if available, otherwise use extracted value
            if (!string.IsNullOrEmpty(retryAfterHeaderValue) && int.TryParse(retryAfterHeaderValue, out int headerValue))
            {
                retryAfterSeconds = headerValue;
            }

            // Determine if this is a token-related or organizational rate limit for better logging
            bool isTokenRateLimit = IsTokenRateLimit(responseContent);

            // Use the actual rate limit name from the API response instead of generic categorization
            string rateLimitType;
            if (!string.IsNullOrEmpty(limitKey) && !string.IsNullOrEmpty(limitNamespace))
            {
                rateLimitType = $"{limitNamespace}.{limitKey}";
            }
            else if (!string.IsNullOrEmpty(limitKey))
            {
                rateLimitType = limitKey;
            }
            else if (!string.IsNullOrEmpty(limitNamespace))
            {
                rateLimitType = limitNamespace;
            }
            else
            {
                // Fallback to generic categorization if we can't extract specific rate limit info
                rateLimitType = isTokenRateLimit ? "Token" : "Organizational";
            }

            // Suppressed logging: Only log rate limit details on first attempt to reduce noise
            if (attempt == 1)
            {
                lock (_counterLock)
                {
                    // Enhanced logging with actual rate limit details when available
                    if (limitValue.HasValue && !string.IsNullOrEmpty(limitKey))
                    {
                        _logger?.LogInformation(
                            "Rate limit: {RateLimitType} rate limit encountered (Retry-After: {RetryAfter}s, Limit: {LimitValue}) - Processing stats: {Processed} processed, {Errors} errors, {RateLimits} rate limits",
                            rateLimitType,
                            retryAfterSeconds,
                            limitValue.Value,
                            _totalProcessed,
                            _totalErrors,
                            _totalRateLimits);
                    }
                    else
                    {
                        _logger?.LogInformation(
                            "Rate limit: {RateLimitType} rate limit encountered (Retry-After: {RetryAfter}s) - Processing stats: {Processed} processed, {Errors} errors, {RateLimits} rate limits",
                            rateLimitType,
                            retryAfterSeconds,
                            _totalProcessed,
                            _totalErrors,
                            _totalRateLimits);
                    }
                }
            }

            // Enhanced logic: When a 429 occurs, if it's a token limit, request a new token
            if (isTokenRateLimit)
            {
                // Log token refresh attempt for better visibility
                if (attempt == 1)
                {
                    _logger?.LogInformation("Rate limit: {RateLimitType} detected - attempting token refresh instead of waiting {RetryAfter}s",
                        rateLimitType, retryAfterSeconds);
                }

                string? newApiKey = RefreshApiKey(responseContent);
                if (newApiKey != null)
                {
                    // Successfully refreshed token
                    apiKey = newApiKey;
                    tokenRefreshAttempted = true;

                    // Log successful token refresh for better visibility
                    _logger?.LogInformation("Rate limit: Successfully refreshed API key for {RateLimitType} - retrying immediately with new token",
                        rateLimitType);

                    // Return immediately to retry with new token (no wait)
                    return (apiKey, shouldRetry, tokenRefreshAttempted);
                }
                else
                {
                    // Failed to refresh token for token-related rate limit
                    tokenRefreshAttempted = true;

                    // Log token refresh failure for better visibility
                    _logger?.LogWarning("Rate limit: Failed to refresh API key for {RateLimitType} - falling back to wait strategy ({RetryAfter}s)",
                        rateLimitType, retryAfterSeconds);

                    // If unable to get new token, honor the rate limit wait
                    // Fall through to wait logic below
                }
            }

            // For non-token rate limits OR when token refresh failed, use wait strategy
            int waitTime;

            if (retryAfterSeconds > 0)
            {
                // Respect the Retry-After header value, but cap it at our maximum
                waitTime = Math.Min(retryAfterSeconds, MAX_WAIT_TIME_SECONDS);
            }
            else
            {
                // No Retry-After header, use default wait time
                waitTime = isTokenRateLimit ? 60 : 60; // Default 60 seconds for both types when no retry-after
            }

            // Suppressed: Only log wait time on first attempt to reduce verbosity
            if (attempt == 1)
            {
                string waitReason = isTokenRateLimit && tokenRefreshAttempted ?
                    "token refresh failed" :
                    "organizational rate limit";

                _logger?.LogInformation("Rate limit: Waiting {WaitTime}s ({Reason}) - will continue processing after wait",
                    waitTime, waitReason);
            }

            System.Threading.Thread.Sleep(waitTime * 1000);

            return (apiKey, shouldRetry, tokenRefreshAttempted);
        }

        /// <summary>
        /// Gets the current processing statistics
        /// </summary>
        public static (int processed, int errors, int rateLimits) GetProcessingStats()
        {
            lock (_counterLock)
            {
                return (_totalProcessed, _totalErrors, _totalRateLimits);
            }
        }

        /// <summary>
        /// Resets the processing statistics counters
        /// </summary>
        public static void ResetProcessingStats()
        {
            lock (_counterLock)
            {
                _totalProcessed = 0;
                _totalErrors = 0;
                _totalRateLimits = 0;
            }
        }

        /// <summary>
        /// Records a processing error for tracking
        /// </summary>
        public static void RecordProcessingError()
        {
            lock (_counterLock)
            {
                _totalErrors++;
            }
        }

        /// <summary>
        /// Records a successful API call for tracking
        /// </summary>
        public static void RecordSuccessfulApiCall()
        {
            lock (_counterLock)
            {
                _totalProcessed++;
            }
        }
    }

    /// <summary>
    /// Tracks conversation processing for ensuring job failure if any conversations fail
    /// </summary>
    public class ConversationProcessingTracker
    {
        private static readonly List<string> _failedConversations = new List<string>();
        private static readonly object _failedConversationsLock = new object();
        private static int _totalConversationsProcessed = 0;
        private static int _totalConversationsFailed = 0;
        private readonly ILogger? _logger;

        public ConversationProcessingTracker(ILogger? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// Records a failed conversation for tracking
        /// </summary>
        public void RecordFailedConversation(string conversationId, string error)
        {
            lock (_failedConversationsLock)
            {
                _failedConversations.Add($"{conversationId}: {error}");
                _totalConversationsFailed++;

                // Log every 10th failure to reduce noise but maintain visibility
                if (_totalConversationsFailed % 10 == 0)
                {
                    _logger?.LogWarning("Conversation processing: {Failed} conversations have failed out of {Total} processed. Recent failure: {ConversationId}",
                        _totalConversationsFailed, _totalConversationsProcessed, conversationId);
                }
            }
        }

        /// <summary>
        /// Records a successfully processed conversation
        /// </summary>
        public void RecordSuccessfulConversation(string conversationId)
        {
            lock (_failedConversationsLock)
            {
                _totalConversationsProcessed++;

                // Log progress every 100 conversations
                if (_totalConversationsProcessed % 100 == 0)
                {
                    _logger?.LogInformation("Conversation processing: {Processed} conversations processed, {Failed} failed",
                        _totalConversationsProcessed, _totalConversationsFailed);
                }
            }
        }

        /// <summary>
        /// Gets the current processing statistics
        /// </summary>
        public (int processed, int failed, List<string> failedConversations) GetProcessingStats()
        {
            lock (_failedConversationsLock)
            {
                return (_totalConversationsProcessed, _totalConversationsFailed, new List<string>(_failedConversations));
            }
        }

        /// <summary>
        /// Checks if any conversations have failed and throws an exception if so
        /// </summary>
        public void EnsureNoFailedConversations()
        {
            lock (_failedConversationsLock)
            {
                if (_totalConversationsFailed > 0)
                {
                    string errorMessage = $"Job failed: {_totalConversationsFailed} out of {_totalConversationsProcessed} conversations failed to process. " +
                                        $"First few failures: {string.Join("; ", _failedConversations.Take(5))}";

                    _logger?.LogError("Job failure: {Failed} conversations failed out of {Total} processed",
                        _totalConversationsFailed, _totalConversationsProcessed);

                    throw new InvalidOperationException(errorMessage);
                }
            }
        }

        /// <summary>
        /// Resets all tracking counters
        /// </summary>
        public static void Reset()
        {
            lock (_failedConversationsLock)
            {
                _failedConversations.Clear();
                _totalConversationsProcessed = 0;
                _totalConversationsFailed = 0;
            }
        }
    }

    /// <summary>
    /// Represents an HTTP response with status code and content
    /// </summary>
    public class HttpApiResponse
    {
        public int StatusCode { get; set; }
        public string Content { get; set; } = string.Empty;
        public bool IsSuccess => StatusCode >= 200 && StatusCode < 300;
        public bool IsAccepted => StatusCode == 202;
        public string StatusDescription { get; set; } = string.Empty;
    }

    public class JsonUtils
    {
        public int MaxPages { get; set; }
        public int RateLimitTimeToGo { get; set; }
        public string? responseCode { get; set; }
        public HttpResponseHeaders? responseHeaders { get; set; }
        private readonly ILogger? _logger;
        private readonly GCUtils _gcUtils;
        private readonly RateLimitHandler _rateLimitHandler;
        private readonly PermissionErrorTracker _permissionErrorTracker;

        public JsonUtils()
        {
            _gcUtils = new GCUtils();
            _rateLimitHandler = new RateLimitHandler(null, _gcUtils);
            _permissionErrorTracker = new PermissionErrorTracker(null);
        }

        public JsonUtils(ILogger? logger)
        {
            _logger = logger;
            _gcUtils = new GCUtils(logger);
            _rateLimitHandler = new RateLimitHandler(logger, _gcUtils);
            _permissionErrorTracker = new PermissionErrorTracker(logger);
        }

        // Store the last response content to check for token-related rate limits
        private string _lastResponseContent = string.Empty;

        internal JArray JsonReturn(string URI, string apiKey)
        {
            int MaxPagesToRetrieve = 30;
            int attempts = 1;
            const int maxAttempts = 5;
            JArray json = null;

            while (attempts <= maxAttempts)
            {
                try
                {
                    // Use the improved JsonReturnHttpResponseGet method for proper rate limit handling
                    var response = JsonReturnHttpResponseGet(URI, apiKey);

                    // Check HTTP status code before processing JSON
                    if (response.StatusCode == 200)
                    {
                        // Success - record successful API call
                        RateLimitHandler.RecordSuccessfulApiCall();
                    }
                    else if (response.StatusCode == 429)
                    {
                        // Rate limiting - JsonReturnHttpResponseGet already handled internal retries
                        // If we get here, it means the internal retries were exhausted
                        // Implement proper rate limiting with 60-second default wait and Retry-After header support
                        _logger?.LogWarning("Rate limit encountered in JsonReturn for URI: {URI} after internal retries. Attempting API key refresh and retry {Attempt}/{MaxAttempts}", URI, attempts, maxAttempts);

                        if (attempts >= maxAttempts)
                        {
                            _logger?.LogError("Rate limiting exceeded retry limit in JsonReturn for URI: {URI} after {MaxAttempts} attempts", URI, maxAttempts);
                            return null; // Return null to maintain backward compatibility
                        }

                        // Use the centralized rate limit handler for consistent behavior
                        var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                            string.Empty, // No retry-after header available at this level
                            response.Content,
                            attempts,
                            apiKey);

                        apiKey = rateLimitResult.apiKey;

                        if (!rateLimitResult.shouldRetry)
                        {
                            _logger?.LogError("Rate limiting exceeded retry limit in JsonReturn for URI: {URI} after centralized rate limit handling", URI);
                            return null; // Return null to maintain backward compatibility
                        }

                        attempts++;
                        continue; // Retry the entire call
                    }
                    else if (response.StatusCode == 403)
                    {
                        _logger?.LogWarning("Access forbidden in JsonReturn for URI: {URI}. Response: {Response}", URI, response.Content);
                        return null; // Return null to maintain backward compatibility
                    }
                    else
                    {
                        _logger?.LogWarning("API call failed in JsonReturn for URI: {URI} with status {StatusCode}: {Response}", URI, response.StatusCode, response.Content);
                        return null; // Return null to maintain backward compatibility
                    }

                    string jsonData = response.Content;

                    // Validate that we have valid JSON data
                    if (string.IsNullOrWhiteSpace(jsonData) || jsonData.Length < 10)
                    {
                        _logger?.LogWarning("Empty or invalid JSON response in JsonReturn for URI: {URI}", URI);
                        return null;
                    }

                    // Parse pageCount for backward compatibility
                    try
                    {
                        int lastchar = jsonData.LastIndexOf(@"}");
                        int pageCount = jsonData.IndexOf(@"""pageCount"":");

                        if (pageCount > 0 && lastchar > pageCount)
                        {
                            MaxPages = int.Parse(jsonData.Substring(pageCount + 12, (lastchar - (pageCount + 12))));
                            if (MaxPages > MaxPagesToRetrieve)
                            {
                                MaxPages = MaxPagesToRetrieve;
                            }
                        }
                        else
                        {
                            MaxPages = 1; // Default to 1 if pageCount not found
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Failed to parse pageCount in JsonReturn for URI: {URI}. Defaulting to 1 page.", URI);
                        MaxPages = 1;
                    }

                    // Extract entities array for backward compatibility
                    try
                    {
                        int start = jsonData.IndexOf("[");
                        int end = jsonData.LastIndexOf("]");

                        if (start >= 0 && end > start)
                        {
                            jsonData = jsonData.Substring(start, (end - (start - 1)));
                        }

                        if (jsonData.IndexOf("[") >= 0)
                        {
                            json = JArray.Parse(jsonData) as JArray;
                        }
                        else
                        {
                            json = JArray.Parse("[ " + jsonData + "]") as JArray;
                        }

                        return json; // Success - return the parsed JArray
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger?.LogError(jsonEx, "JSON parsing failed in JsonReturn for URI: {URI}. Response: {Response}", URI, jsonData);
                        return null; // Return null to maintain backward compatibility
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Exception in JsonReturn attempt {Attempt}/{MaxAttempts} for URI: {URI}", attempts, maxAttempts, URI);

                    if (attempts >= maxAttempts)
                    {
                        _logger?.LogError("JsonReturn failed after {MaxAttempts} attempts for URI: {URI}", maxAttempts, URI);
                        return null; // Return null to maintain backward compatibility
                    }

                    attempts++;
                    // Add a small delay before retry
                    System.Threading.Thread.Sleep(1000 * attempts);
                }
            }

            return null; // Return null if all attempts failed
        }

        internal string JsonReturnString(string URI, string apiKey)
        {
            int attempts = 1;
            string jsonData = string.Empty;
            responseCode = string.Empty;

            while (attempts <= 5) // Use <= to match the MAX_RETRY_ATTEMPTS constant
            {
                Task<string> result = JsonReturnStringGetAsync(URI, apiKey);
                var finalResult = result.Result;
                // Handle successful responses (HTTP 200) and HTTP 202 Accepted responses
                if (finalResult != null && (responseCode == string.Empty || responseCode == "Accepted"))
                {
                    jsonData = finalResult.ToString();
                    break;
                }
                else
                {
                    attempts++;

                    switch (responseCode)
                    {
                        case "Forbidden":
                            // Use the permission error tracker to determine if this is a temporary or permanent issue
                            string resourceKey = URI.Split('?')[0]; // Remove query parameters to get the base resource

                            // Check for permanent permission issues (missing permissions) first
                            if (_lastResponseContent != null &&
                                (_lastResponseContent.Contains("missing.division.permission") ||
                                 _lastResponseContent.Contains("missing.permission") ||
                                 _lastResponseContent.Contains("You are missing the following permission")))
                            {
                                _logger?.LogError("Permanent permission issue detected for resource {Resource}. Missing required permissions. Throwing UnauthorizedAccessException", resourceKey);
                                throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                            }

                            // For other forbidden errors, use the permission error tracker
                            bool isPermanentError = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                            if (isPermanentError)
                            {
                                _logger?.LogError("Permanent access forbidden for resource {Resource}. Throwing UnauthorizedAccessException", resourceKey);
                                throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                            }
                            else
                            {
                                _logger?.LogWarning("Temporary access forbidden for resource {Resource}. Will retry.", resourceKey);
                                // Add exponential backoff delay based on attempt number
                                int delayMs = (int)Math.Pow(2, attempts) * 1000;
                                Thread.Sleep(delayMs);
                                responseCode = "";
                                jsonData = null;
                                break;
                            }

                        case "429":
                        case "TooManyRequests":
                            string retryAfter = string.Empty;

                            if (responseHeaders != null && responseHeaders.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            // Only log rate limit on first attempt to reduce verbosity
                            if (attempts == 1)
                            {
                                _logger?.LogInformation("Rate limit encountered. Retry-After: {RetryAfter} seconds", retryAfter);
                            }

                            // Use the standardized rate limit handler
                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                _lastResponseContent,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogWarning("Rate limiting exceeded retry limit after {Attempts} attempts. Returning structured error response instead of throwing exception.", attempts);
                                // Return structured error response instead of throwing exception
                                jsonData = "{\"error\": true, \"message\": \"Rate limiting exceeded retry limit\", \"statusCode\": \"TooManyRequests\", \"attempts\": " + attempts + "}";
                                attempts = 6; // Exit the loop
                                break;
                            }

                            // Removed verbose success logging to reduce log noise
                            responseCode = "";
                            jsonData = null;
                            break;

                        case "NotFound":
                            _logger?.LogWarning("NotFound: Resource not found at {URI}", URI);
                            jsonData = "{\"error\": true, \"message\": \"Resource not found\", \"statusCode\": \"NotFound\"}";
                            attempts = 6; // Exit the loop
                            break;
                    }
                }
            }

            return jsonData;
        }

        internal string JsonReturnString(string URI, string apiKey, string selectBody)
        {
            int attempts = 1;
            string jsonData = string.Empty;
            responseCode = string.Empty;

            while (attempts <= 5) // Use <= to match the MAX_RETRY_ATTEMPTS constant
            {
                Task<string> result = JsonReturnStringPostAsync(URI, apiKey, selectBody);
                var finalResult = result.Result;
                if (finalResult != null)
                {
                    // Check for common error patterns in the response
                    if (finalResult.ToString().Contains("Access Forbidden"))
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (finalResult.ToString().Contains("missing.division.permission") ||
                            finalResult.ToString().Contains("missing.permission") ||
                            finalResult.ToString().Contains("missing.any.division.permissions") ||
                            finalResult.ToString().Contains("You are missing the following permission") ||
                            finalResult.ToString().Contains("\"isPermanent\": true"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions in response: {Response}", resourceKey, finalResult);
                            throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource} in response: {Response}", resourceKey, finalResult);
                            throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource} in response: {Response}", resourceKey, finalResult);
                            // Return a JSON with error information but don't throw an exception
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": false}}";
                        }
                    }

                    // Check for JSON parsing errors in the response
                    if (finalResult.ToString().Contains("After parsing a value an unexpected character was encountered") ||
                        finalResult.ToString().Contains("Unexpected character encountered while parsing value"))
                    {
                        _logger?.LogError("JSON parsing error in response: {Response}", finalResult);
                        // Return a valid JSON with error information instead of throwing
                        return $"{{\"error\": true, \"message\": \"JSON parsing error: {finalResult.Replace("\"", "\\\"")}\", \"statusCode\": \"BadRequest\"}}";
                    }

                    jsonData = finalResult.ToString();
                    break;
                }
                else
                {
                    attempts++;
                    switch (responseCode)
                    {
                        case "429":
                        case "TooManyRequests":
                            string retryAfter = string.Empty;

                            if (responseHeaders != null && responseHeaders.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            // Only log rate limit on first attempt to reduce verbosity
                            if (attempts == 1)
                            {
                                _logger?.LogInformation("Rate limit encountered on POST. Retry-After: {RetryAfter} seconds", retryAfter);
                            }

                            // Use the standardized rate limit handler
                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                _lastResponseContent,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogWarning("Rate limiting exceeded retry limit after {Attempts} attempts on POST request. Returning structured error response instead of throwing exception.", attempts);
                                // Return structured error response instead of throwing exception
                                jsonData = "{\"error\": true, \"message\": \"Rate limiting exceeded retry limit\", \"statusCode\": \"TooManyRequests\", \"attempts\": " + attempts + "}";
                                attempts = 6; // Exit the loop
                                break;
                            }

                            // Removed verbose success logging to reduce log noise
                            responseCode = "";
                            jsonData = null;
                            break;

                        case "NotFound":
                            _logger?.LogInformation("Resource not found at {URI}", URI);
                            jsonData = "{}";
                            attempts = 6; // Exit the loop
                            break;

                        case "Forbidden":
                            // Extract resource key from URI (use the endpoint path without query parameters)
                            string resourceKey = URI.Split('?')[0];

                            // Check for permanent permission issues (missing permissions) first
                            if (_lastResponseContent != null &&
                                (_lastResponseContent.Contains("missing.division.permission") ||
                                 _lastResponseContent.Contains("missing.permission") ||
                                 _lastResponseContent.Contains("You are missing the following permission")))
                            {
                                _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Exiting retry loop.", resourceKey);
                                jsonData = $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: Missing required permissions\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                                attempts = 6; // Exit the loop
                            }
                            else
                            {
                                // For other forbidden errors, use the permission error tracker
                                bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                                if (isPermanentIssue)
                                {
                                    _logger?.LogError("Permanent permission issue detected for {Resource}. Exiting retry loop.", resourceKey);
                                    jsonData = $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                                    attempts = 6; // Exit the loop
                                }
                                else
                                {
                                    _logger?.LogWarning("Temporary permission issue detected for {Resource}. Will retry.", resourceKey);
                                    // Add exponential backoff delay based on attempt number
                                    int delayMs = (int)Math.Pow(2, attempts) * 1000;
                                    if (delayMs > 30000) delayMs = 30000; // Cap at 30 seconds

                                    _logger?.LogInformation("Waiting {Delay}ms before retry attempt {Attempt}", delayMs, attempts);
                                    Thread.Sleep(delayMs);

                                    responseCode = "";
                                    jsonData = null;
                                }
                            }
                            break;
                    }
                }
            }
            return jsonData;
        }

        /// <summary>
        /// Makes an HTTP POST request and returns both status code and response content
        /// This method properly handles HTTP 202 Accepted responses for asynchronous operations
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="selectBody">The JSON body to send</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal async Task<HttpApiResponse> JsonReturnHttpResponseAsync(string URI, string apiKey, string selectBody)
        {
            var response = new HttpApiResponse();
            int attempts = 1;
            const int maxAttempts = 5;

            while (attempts <= maxAttempts)
            {
                try
                {
                    using (var handler = new HttpClientHandler())
                    using (var client = new HttpClient(handler) { Timeout = TimeSpan.FromMinutes(5) })
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);

                        var httpResponse = await client.PostAsync(URI, new StringContent(selectBody, Encoding.UTF8, "application/json"));

                        response.StatusCode = (int)httpResponse.StatusCode;
                        response.StatusDescription = httpResponse.ReasonPhrase ?? string.Empty;
                        response.Content = await httpResponse.Content.ReadAsStringAsync();

                        // Handle different status codes appropriately
                        if (httpResponse.StatusCode == HttpStatusCode.OK || httpResponse.StatusCode == HttpStatusCode.Accepted)
                        {
                            // Both 200 OK and 202 Accepted are valid responses
                            return response;
                        }
                        else if (httpResponse.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            // Handle rate limiting
                            string retryAfter = string.Empty;
                            if (httpResponse.Headers.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                response.Content,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                response.StatusCode = 429;
                                response.Content = "Rate limiting exceeded retry limit";
                                return response;
                            }

                            attempts++;
                            continue;
                        }
                        else
                        {
                            // For other HTTP status codes, return the response as-is
                            // This allows the caller to handle specific status codes appropriately
                            return response;
                        }
                    }
                }
                catch (TaskCanceledException ex)
                {
                    bool isTimeout = ex.InnerException is TimeoutException ||
                                     (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                     ex.Message.Contains("timed out") ||
                                     ex.Message.Contains("timeout");

                    if (isTimeout)
                    {
                        _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                        response.StatusCode = 408; // Request Timeout
                        response.Content = "Request timed out";
                        return response;
                    }
                    else
                    {
                        _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                        attempts++;
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Exception during HTTP request to {URI}", URI);
                    attempts++;
                    if (attempts > maxAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"Request failed after {maxAttempts} attempts: {ex.Message}";
                        return response;
                    }
                    continue;
                }
            }

            response.StatusCode = 500;
            response.Content = $"Request failed after {maxAttempts} attempts";
            return response;
        }

        /// <summary>
        /// Synchronous wrapper for JsonReturnHttpResponseAsync
        /// Makes an HTTP POST request and returns both status code and response content
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="selectBody">The JSON body to send</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal HttpApiResponse JsonReturnHttpResponse(string URI, string apiKey, string selectBody)
        {
            var task = JsonReturnHttpResponseAsync(URI, apiKey, selectBody);
            return task.Result;
        }

        /// <summary>
        /// Makes an HTTP GET request and returns both status code and response content
        /// This method provides consistent error handling and status code detection for GET requests
        /// Enhanced for analytics API endpoints with improved rate limiting handling
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal async Task<HttpApiResponse> JsonReturnHttpResponseGetAsync(string URI, string apiKey)
        {
            var response = new HttpApiResponse();
            int rateLimitAttempts = 0; // Separate counter for rate limit attempts
            const int maxGeneralAttempts = 3; // Reduced for non-rate-limit errors
            const int maxRateLimitAttempts = 10; // Dedicated counter for rate limiting
            int generalAttempts = 1;

            while (generalAttempts <= maxGeneralAttempts)
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(5);
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                        var httpResponse = await client.GetAsync(URI);
                        response.StatusCode = (int)httpResponse.StatusCode;
                        response.StatusDescription = httpResponse.ReasonPhrase ?? string.Empty;
                        response.Content = await httpResponse.Content.ReadAsStringAsync();

                        // Handle rate limiting with dedicated retry logic
                        if (httpResponse.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            rateLimitAttempts++;

                            if (rateLimitAttempts > maxRateLimitAttempts)
                            {
                                _logger?.LogError("Rate limiting exceeded maximum retry attempts ({MaxAttempts}) for URI: {URI}", maxRateLimitAttempts, URI);
                                response.StatusCode = 429;
                                response.Content = "Rate limiting exceeded retry limit";
                                return response;
                            }

                            string retryAfter = string.Empty;
                            if (httpResponse.Headers.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            // Only log rate limit on first attempt to reduce verbosity
                            if (rateLimitAttempts == 1)
                            {
                                _logger?.LogWarning("Rate limit encountered for URI: {URI}", URI);
                            }

                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                response.Content,
                                rateLimitAttempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            // Continue the rate limit retry loop without incrementing general attempts
                            continue;
                        }

                        // For successful responses or non-rate-limit errors, return immediately
                        if (response.StatusCode == 200 || response.StatusCode < 500)
                        {
                            // Record successful API call for 200 responses
                            if (response.StatusCode == 200)
                            {
                                RateLimitHandler.RecordSuccessfulApiCall();
                            }

                            // Only log success message if there were multiple rate limit retries
                            if (rateLimitAttempts > 3)
                            {
                                _logger?.LogInformation("Request completed after {RateLimitAttempts} rate limit retries", rateLimitAttempts);
                            }
                            return response;
                        }

                        // For 5xx server errors, use general retry logic
                        _logger?.LogWarning("Server error {StatusCode} on attempt {GeneralAttempt}/{MaxGeneralAttempts} for URI: {URI}",
                            response.StatusCode, generalAttempts, maxGeneralAttempts, URI);

                        if (generalAttempts >= maxGeneralAttempts)
                        {
                            return response;
                        }

                        generalAttempts++;
                        await Task.Delay(1000 * generalAttempts); // Exponential backoff for server errors
                    }
                }
                catch (TaskCanceledException ex)
                {
                    bool isTimeout = ex.InnerException is TimeoutException ||
                                     (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                     ex.Message.Contains("timed out") ||
                                     ex.Message.Contains("timeout");

                    if (isTimeout)
                    {
                        _logger?.LogWarning(ex, "GET request timed out after {Timeout} minutes for URI: {URI}", 5, URI);
                        response.StatusCode = 408; // Request Timeout
                        response.Content = $"{{\"error\": true, \"message\": \"Request timed out after 5 minutes\", \"statusCode\": \"RequestTimeout\"}}";
                        return response;
                    }
                    else
                    {
                        _logger?.LogWarning(ex, "GET request cancelled (not timeout related) for URI: {URI}", URI);
                        response.StatusCode = 499; // Client Closed Request
                        response.Content = $"{{\"error\": true, \"message\": \"Request was cancelled\", \"statusCode\": \"RequestCancelled\"}}";
                        return response;
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger?.LogError(ex, "HTTP error on GET request attempt {GeneralAttempt}/{MaxGeneralAttempts} for URI: {URI}", generalAttempts, maxGeneralAttempts, URI);

                    if (generalAttempts >= maxGeneralAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"{{\"error\": true, \"message\": \"HTTP error after {maxGeneralAttempts} attempts: {ex.Message}\", \"statusCode\": \"HttpRequestException\"}}";
                        return response;
                    }

                    generalAttempts++;
                    await Task.Delay(1000 * generalAttempts); // Exponential backoff
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Unexpected error on GET request attempt {GeneralAttempt}/{MaxGeneralAttempts} for URI: {URI}", generalAttempts, maxGeneralAttempts, URI);

                    if (generalAttempts >= maxGeneralAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"{{\"error\": true, \"message\": \"Unexpected error after {maxGeneralAttempts} attempts: {ex.Message}\", \"statusCode\": \"UnexpectedException\"}}";
                        return response;
                    }

                    generalAttempts++;
                    await Task.Delay(1000 * generalAttempts); // Exponential backoff
                }
            }

            // This should never be reached, but just in case
            response.StatusCode = 500;
            response.Content = $"{{\"error\": true, \"message\": \"Maximum retry attempts exceeded\", \"statusCode\": \"MaxRetriesExceeded\"}}";
            return response;
        }

        /// <summary>
        /// Synchronous wrapper for JsonReturnHttpResponseGetAsync
        /// Makes an HTTP GET request and returns both status code and response content
        /// Enhanced with proper exception handling to prevent NullReferenceException
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal HttpApiResponse JsonReturnHttpResponseGet(string URI, string apiKey)
        {
            try
            {
                var task = JsonReturnHttpResponseGetAsync(URI, apiKey);
                var result = task.Result;

                // Defensive null check - should never be null but adding safety
                if (result == null)
                {
                    _logger?.LogError("JsonReturnHttpResponseGetAsync returned null for URI: {URI}", URI);
                    return new HttpApiResponse
                    {
                        StatusCode = 500,
                        Content = "{\"error\": true, \"message\": \"Internal error: null response from async method\", \"statusCode\": \"InternalServerError\"}",
                        StatusDescription = "Internal Server Error"
                    };
                }

                return result;
            }
            catch (AggregateException ex)
            {
                // Handle exceptions from async task
                var innerException = ex.InnerException ?? ex;
                _logger?.LogError(innerException, "Exception in JsonReturnHttpResponseGet for URI: {URI}", URI);

                return new HttpApiResponse
                {
                    StatusCode = 500,
                    Content = $"{{\"error\": true, \"message\": \"Request failed: {innerException.Message.Replace("\"", "\\\"")}\", \"statusCode\": \"RequestException\"}}",
                    StatusDescription = "Request Exception"
                };
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                _logger?.LogError(ex, "Unexpected exception in JsonReturnHttpResponseGet for URI: {URI}", URI);

                return new HttpApiResponse
                {
                    StatusCode = 500,
                    Content = $"{{\"error\": true, \"message\": \"Unexpected error: {ex.Message.Replace("\"", "\\\"")}\", \"statusCode\": \"UnexpectedException\"}}",
                    StatusDescription = "Unexpected Exception"
                };
            }
        }

        internal async Task<string> JsonReturnStringPostAsync(string URI, string apiKey, string selectBody)
        {
            using (var cts = new CancellationTokenSource())
            {
                try
                {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                // Increase timeout to handle slow API responses
                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(5) };



                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);

                Response = await Client.PostAsync(URI, new StringContent(selectBody, Encoding.UTF8, "application/json"));

                // HTTP 200 OK and 202 Accepted are both considered successful responses
                if (Response.StatusCode == HttpStatusCode.OK || Response.StatusCode == HttpStatusCode.Accepted)
                {
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Validate that the response content is likely JSON before returning
                    if (string.IsNullOrWhiteSpace(Contents) || (!Contents.TrimStart().StartsWith("{") && !Contents.TrimStart().StartsWith("[")))
                    {
                        _logger?.LogError("POST request to {URI} returned HTTP {StatusCode} but non-JSON content: {ContentPreview}",
                            URI, Response.StatusCode, Contents?.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents);
                        throw new HttpRequestException($"API returned non-JSON content: {Contents?.Substring(0, Math.Min(Contents.Length, 200))}", null, Response.StatusCode);
                    }

                    if (Response.StatusCode == HttpStatusCode.Accepted)
                    {
                        _logger?.LogDebug("POST request to {URI} returned HTTP 202 Accepted (async operation started)", URI);
                    }
                    else
                    {
                        _logger?.LogDebug("POST request to {URI} successful with valid JSON response", URI);
                    }

                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;
                }
                else
                {
                    responseCode = Response.StatusCode.ToString();
                    responseHeaders = Response.Headers;
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Store the response content for checking token-related rate limits
                    _lastResponseContent = Contents;

                    // Handle rate limiting first to avoid verbose JSON logging for 429 errors
                    if (responseCode == "TooManyRequests")
                    {
                        // Extract retry-after header if available
                        string retryAfter = string.Empty;
                        if (Response.Headers.TryGetValues("Retry-After", out var values))
                        {
                            retryAfter = values.First();
                        }

                        // Use the standardized rate limit handler for clean, concise logging
                        var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                            retryAfter,
                            Contents,
                            1, // First attempt in this method
                            apiKey);

                        // Return null to signal that the request should be retried with the new API key
                        return null;
                    }

                    // Optimized error logging with context-aware log levels and structured information
                    LogApiError(responseCode, URI, selectBody, Contents, Response.Headers);

                    // For all error responses, return a valid JSON with error information
                    string safeContent = Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n");

                    // Handle specific error types with custom messages
                    if (responseCode == "Forbidden")
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (Contents.Contains("missing.division.permission") ||
                            Contents.Contains("missing.permission") ||
                            Contents.Contains("missing.any.division.permissions") ||
                            Contents.Contains("You are missing the following permission"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Returning permanent error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: Missing required permissions\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Returning permanent error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: {Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n")}\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource}. Returning temporary error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": false}}";
                        }
                    }
                    else if (responseCode == "BadRequest")
                    {
                        // HTTP 400 Bad Request indicates a client-side error that should halt processing
                        string errorMessage = $"HTTP 400 Bad Request from {URI}: {Contents}";
                        _logger?.LogError("API Error: {ErrorMessage}", errorMessage);

                        // Throw an exception to halt processing - HTTP 400 errors should not be silently handled
                        throw new HttpRequestException(errorMessage, null, HttpStatusCode.BadRequest);
                    }
                    else
                    {
                        // Check if this is a critical client error that should halt processing
                        if (responseCode == "Unauthorized" || responseCode == "NotFound" || responseCode == "MethodNotAllowed" ||
                            responseCode == "NotAcceptable" || responseCode == "Conflict" || responseCode == "Gone" ||
                            responseCode == "UnprocessableEntity" || responseCode == "InternalServerError")
                        {
                            // These are critical errors that indicate fundamental issues
                            string criticalErrorMessage = $"HTTP {responseCode} from {URI}: {Contents}";
                            _logger?.LogError("Critical API Error: {ErrorMessage}", criticalErrorMessage);

                            // Throw an exception for critical errors
                            throw new HttpRequestException(criticalErrorMessage, null, (HttpStatusCode)Enum.Parse(typeof(HttpStatusCode), responseCode));
                        }
                        else
                        {
                            // For other errors (like temporary server issues), return error JSON to allow retry
                            _logger?.LogWarning("{ResponseCode}: Returning error JSON for potential retry", responseCode);
                            // Create a safe version of the content for the error message
                            string safeCont = Contents.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents;

                            return $"{{\"error\": true, \"message\": \"{responseCode}: {safeCont}\", \"statusCode\": \"{responseCode}\"}}";
                        }
                    }


                }
            }
            catch (TaskCanceledException ex)
            {
                // Check if this is a timeout
                bool isTimeout = ex.InnerException is TimeoutException ||
                                 (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                 ex.Message.Contains("timed out") ||
                                 ex.Message.Contains("timeout");

                if (isTimeout)
                {
                    _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                    // Return a valid JSON with timeout error information
                    return $"{{\"error\": true, \"message\": \"Error calling PostToken: The operation has timed out.\", \"statusCode\": \"RequestTimeout\"}}";
                }
                else
                {
                    _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception caught in JsonReturnStringPostAsync Module. Inner Exception: {InnerException}",
                    ex.InnerException?.Message ?? "None");
                return ex.Message;
            }
            }
        }

        internal async Task<string> JsonReturnStringGetAsync(string URI, string apiKey)
        {
            using (var cts = new CancellationTokenSource())
            {
                try
                {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                // Increase timeout to handle slow API responses
                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(5) };


                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);
                Client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                Response = await Client.GetAsync(URI);

                // Handle both HTTP 200 (success) and HTTP 202 (accepted for async operations)
                if (Response.StatusCode == HttpStatusCode.OK)
                {
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Validate that the response content is likely JSON before returning
                    if (string.IsNullOrWhiteSpace(Contents) || (!Contents.TrimStart().StartsWith("{") && !Contents.TrimStart().StartsWith("[")))
                    {
                        _logger?.LogError("GET request to {URI} returned HTTP 200 but non-JSON content: {ContentPreview}",
                            URI, Contents?.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents);
                        throw new HttpRequestException($"API returned non-JSON content: {Contents?.Substring(0, Math.Min(Contents.Length, 200))}", null, HttpStatusCode.OK);
                    }

                    // Record successful API call
                    RateLimitHandler.RecordSuccessfulApiCall();

                    // _logger?.LogDebug("GET request to {URI} successful with valid JSON response", URI);
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;
                }
                else if (Response.StatusCode == HttpStatusCode.Accepted)
                {
                    // HTTP 202 Accepted - for async operations like job polling
                    // This is normal for job status checks when the job is still processing
                    var Contents = await Response.Content.ReadAsStringAsync();
                    responseCode = "Accepted";

                    _logger?.LogDebug("GET request to {URI} returned HTTP 202 Accepted (async operation in progress)", URI);
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();

                    // Return empty string to indicate 202 response - calling code can check responseCode
                    return string.Empty;
                }
                else
                {
                    var Contents = await Response.Content.ReadAsStringAsync();
                    responseHeaders = Response.Headers;
                    responseCode = Response.StatusCode.ToString();

                    // Store the response content for checking token-related rate limits
                    _lastResponseContent = Contents;

                    // Handle rate limiting first to avoid verbose JSON logging for 429 errors
                    if (responseCode == "TooManyRequests")
                    {
                        // Extract retry-after header if available
                        string retryAfter = string.Empty;
                        if (Response.Headers.TryGetValues("Retry-After", out var values))
                        {
                            retryAfter = values.First();
                        }

                        // Use the standardized rate limit handler for clean, concise logging
                        var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                            retryAfter,
                            Contents,
                            1, // First attempt in this method
                            apiKey);

                        // Return null to signal that the request should be retried with the new API key
                        return null;
                    }

                    // Optimized error logging with context-aware log levels and structured information
                    LogApiError(responseCode, URI, string.Empty, Contents, Response.Headers);
                    // Special handling for Forbidden - use permission error tracker
                    if (responseCode == "Forbidden")
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (Contents.Contains("missing.division.permission") ||
                            Contents.Contains("missing.permission") ||
                            Contents.Contains("missing.any.division.permissions") ||
                            Contents.Contains("You are missing the following permission"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Throwing UnauthorizedAccessException", resourceKey);
                            throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Throwing UnauthorizedAccessException", resourceKey);
                            throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource}. Returning error JSON to allow retry.", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\"}}";
                        }
                    }
                    else if (responseCode == "BadRequest")
                    {
                        // HTTP 400 Bad Request indicates a client-side error that should halt processing
                        string errorMessage = $"HTTP 400 Bad Request from {URI}: {Contents}";
                        _logger?.LogError("API Error: {ErrorMessage}", errorMessage);

                        // Throw an exception to halt processing - HTTP 400 errors should not be silently handled
                        throw new HttpRequestException(errorMessage, null, HttpStatusCode.BadRequest);
                    }
                    // Check if this is a critical client error that should halt processing
                    else if (responseCode == "Unauthorized" || responseCode == "NotFound" || responseCode == "MethodNotAllowed" ||
                            responseCode == "NotAcceptable" || responseCode == "Conflict" || responseCode == "Gone" ||
                            responseCode == "UnprocessableEntity" || responseCode == "InternalServerError")
                    {
                        // These are critical errors that indicate fundamental issues
                        // Error already logged by LogApiError method above, just throw exception
                        string criticalErrorMessage = $"HTTP {responseCode} from {URI}: {ExtractErrorSummary(Contents)}";
                        throw new HttpRequestException(criticalErrorMessage, null, (HttpStatusCode)Enum.Parse(typeof(HttpStatusCode), responseCode));
                    }
                    else
                    {
                        // For other errors (like temporary server issues), return error JSON to allow retry
                        _logger?.LogWarning("{ResponseCode}: Returning error JSON for potential retry", responseCode);
                        return $"{{\"error\": true, \"message\": \"{Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n")}\", \"statusCode\": \"{responseCode}\"}}";
                    }
                }
            }
            catch (TaskCanceledException ex)
            {
                // Check if this is a timeout
                bool isTimeout = ex.InnerException is TimeoutException ||
                                 (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                 ex.Message.Contains("timed out") ||
                                 ex.Message.Contains("timeout");

                if (isTimeout)
                {
                    _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                    // Return a valid JSON with timeout error information
                    return $"{{\"error\": true, \"message\": \"Error calling PostToken: The operation has timed out.\", \"statusCode\": \"RequestTimeout\"}}";
                }
                else
                {
                    _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception caught in JsonReturnStringGetAsync Module. Inner Exception: {InnerException}",
                    ex.InnerException?.Message ?? "None");
                return null;
            }
            }
        }

        /// <summary>
        /// Deletes notification subscription using modern HttpClient with centralized rate limiting
        /// </summary>
        internal Boolean DeleteNotificationSubscription(string URI, string SessionId, string apiKey)
        {
            string URL = URI + "/api/v2/notifications/channels/" + SessionId + "/subscriptions";
            int attempts = 1;
            const int maxAttempts = 5; // Match other methods

            while (attempts <= maxAttempts)
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    client.Timeout = TimeSpan.FromMinutes(3); // Match other methods

                    try
                    {
                        var response = client.DeleteAsync(URL).Result;

                        if (response.IsSuccessStatusCode)
                        {
                            _logger?.LogDebug("Successfully deleted notification subscription for session {SessionId}", SessionId);
                            return true;
                        }
                        else
                        {
                            var responseContent = response.Content.ReadAsStringAsync().Result;
                            _lastResponseContent = responseContent;

                            // Handle rate limiting using centralized approach
                            if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                _logger?.LogWarning("Rate limit exceeded (DELETE) - Attempt {Attempt}", attempts);

                                string retryAfter = string.Empty;
                                if (response.Headers.TryGetValues("Retry-After", out var values))
                                {
                                    retryAfter = values.First();
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    responseContent,
                                    attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                if (!rateLimitResult.shouldRetry)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts for DELETE {URL}", attempts, URL);
                                    return false;
                                }

                                attempts++;
                                continue;
                            }
                            else if (response.StatusCode == HttpStatusCode.NotFound)
                            {
                                _logger?.LogInformation("Notification subscription not found for session {SessionId} at {URL}", SessionId, URL);
                                return true; // Consider NotFound as success for deletion
                            }
                            else if (response.StatusCode == HttpStatusCode.GatewayTimeout)
                            {
                                _logger?.LogWarning("Gateway timeout encountered for DELETE {URL}, attempt {Attempt}", URL, attempts);
                                if (attempts >= maxAttempts)
                                {
                                    _logger?.LogError("Gateway timeout exceeded retry limit after {Attempts} attempts for DELETE {URL}", attempts, URL);
                                    return false;
                                }
                                attempts++;
                                System.Threading.Thread.Sleep(5000); // Wait 5 seconds for gateway timeout
                                continue;
                            }
                            else
                            {
                                _logger?.LogError("Failed to delete notification subscription. Status Code: {StatusCode}, Response: {Response}",
                                    response.StatusCode, responseContent);
                                return false;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Exception in DeleteNotificationSubscription for URL {URL}, attempt {Attempt}", URL, attempts);
                        if (attempts >= maxAttempts)
                        {
                            return false;
                        }
                        attempts++;
                        System.Threading.Thread.Sleep(1000 * attempts); // Exponential backoff
                    }
                }
            }

            return false;
        }

        internal void ConvJson(dynamic json, ref DataTable DtTemp)

        {
            foreach (dynamic item in json)
            {
                DataRow newRow = DtTemp.NewRow();

                foreach (DataColumn dcTemp in DtTemp.Columns)
                {
                    if (dcTemp.ColumnName != "updated" && item[dcTemp.ToString()] != null)
                    {
                        newRow[dcTemp] = item[dcTemp.ToString()];
                    }
                }
                newRow["updated"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                DtTemp.Rows.Add(newRow);
            }
        }

        internal DataTable CreateTempUserGroupsTable()
        {
            DataTable DtTemp = new DataTable("Users");

            DtTemp.Columns.Add("id", typeof(string));
            DtTemp.Columns.Add("GroupName", typeof(String));
            DtTemp.Columns.Add("updated", typeof(DateTime));

            //Add Key For Emite Purposes
            DataColumn[] key = new DataColumn[1];
            key[0] = DtTemp.Columns[0];
            DtTemp.PrimaryKey = key;

            return DtTemp;
        }

        /// <summary>
        /// Optimized API error logging with context-aware log levels and structured information
        /// </summary>
        /// <param name="responseCode">HTTP response code</param>
        /// <param name="uri">Request URI</param>
        /// <param name="requestBody">Request body content</param>
        /// <param name="responseContent">Response content</param>
        /// <param name="responseHeaders">Response headers</param>
        private void LogApiError(string responseCode, string uri, string requestBody, string responseContent, HttpResponseHeaders responseHeaders)
        {
            // Extract correlation ID for tracking
            string correlationId = string.Empty;
            if (responseHeaders.TryGetValues("ININ-Correlation-Id", out var correlationValues))
            {
                correlationId = correlationValues.First();
            }

            // Handle contact list specific errors with optimized logging
            if (uri.Contains("/api/v2/outbound/contactlists/") && uri.Contains("/export"))
            {
                if (responseCode == "NotFound" && responseContent.Contains("contact.list.not.found"))
                {
                    // Extract contact list ID from URI
                    var uriParts = uri.Split('/');
                    string contactListId = "unknown";
                    for (int i = 0; i < uriParts.Length - 1; i++)
                    {
                        if (uriParts[i] == "contactlists" && i + 1 < uriParts.Length)
                        {
                            contactListId = uriParts[i + 1];
                            break;
                        }
                    }

                    // Log as WARNING since missing contact lists may be expected
                    _logger?.LogWarning("ContactList export failed: List {ContactListId} not found (correlation: {CorrelationId})",
                        contactListId, correlationId);

                    // Debug-level logging for detailed troubleshooting
                    if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
                    {
                        _logger.LogDebug("ContactList {ContactListId} error details - URI: {URI}, Response: {Response}",
                            contactListId, uri, responseContent);
                    }
                    return;
                }
            }

            // Handle other specific error patterns with optimized logging
            switch (responseCode)
            {
                case "NotFound":
                    _logger?.LogWarning("Resource not found: {URI} (correlation: {CorrelationId})", uri, correlationId);
                    break;

                case "Forbidden":
                    _logger?.LogWarning("Access forbidden: {URI} (correlation: {CorrelationId})", uri, correlationId);
                    break;

                case "BadRequest":
                    // Extract key error information from response
                    string errorSummary = ExtractErrorSummary(responseContent);
                    _logger?.LogError("Bad request: {URI} - {ErrorSummary} (correlation: {CorrelationId})",
                        uri, errorSummary, correlationId);
                    break;

                default:
                    // For other errors, use structured logging with essential information
                    string errorMessage = ExtractErrorSummary(responseContent);
                    _logger?.LogError("API Error: HTTP {StatusCode} from {URI} - {ErrorMessage} (correlation: {CorrelationId})",
                        responseCode, uri, errorMessage, correlationId);
                    break;
            }

            // Debug-level logging for comprehensive troubleshooting when needed
            if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
            {
                _logger.LogDebug("API Error Details - URI: {URI}, Status: {StatusCode}, Request: {RequestBody}, Response: {ResponseContent}",
                    uri, responseCode, requestBody, responseContent);

                if (responseHeaders != null)
                {
                    _logger.LogDebug("Response Headers: {Headers}",
                        string.Join(", ", responseHeaders.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));
                }
            }
        }

        /// <summary>
        /// Extracts a concise error summary from API response content
        /// </summary>
        /// <param name="responseContent">Full API response content</param>
        /// <returns>Concise error summary</returns>
        private string ExtractErrorSummary(string responseContent)
        {
            if (string.IsNullOrWhiteSpace(responseContent))
                return "No response content";

            try
            {
                // Try to parse JSON and extract key error information
                var errorObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(responseContent);

                if (errorObj?.message != null)
                {
                    string message = errorObj.message.ToString();
                    // Limit message length for log clarity
                    return message.Length > 100 ? message.Substring(0, 100) + "..." : message;
                }

                if (errorObj?.code != null)
                {
                    return errorObj.code.ToString();
                }
            }
            catch
            {
                // If JSON parsing fails, return truncated content
            }

            // Fallback to truncated content
            return responseContent.Length > 100 ? responseContent.Substring(0, 100) + "..." : responseContent;
        }

        /// <summary>
        /// Checks rate limit headers and applies preventive throttling if needed
        /// </summary>
        internal void CheckRateLimit(WebHeaderCollection Headers)
        {
            int counter = 0;
            int timeToGo = 0;
            bool foundCount = false;
            bool foundReset = false;

            foreach (string key in Headers.AllKeys)
            {
                string value = Headers[key];
                if (key.ToString() == "inin-ratelimit-count")
                {
                    counter = Convert.ToInt32(value);
                    foundCount = true;
                }

                if (key.ToString() == "inin-ratelimit-reset")
                {
                    timeToGo = Convert.ToInt32(value);
                    foundReset = true;
                }
            }

            // Only apply throttling if we found both headers
            if (foundCount && foundReset)
            {
                // Log the current rate limit status
                _logger?.LogDebug("Current rate limit: {Count}/300, reset in {TimeToGo} seconds", counter, timeToGo);

                // Apply preventive throttling when approaching the limit
                if (counter > 290)
                {
                    _logger?.LogWarning("Rate limit count high ({Count}/300), applying preventive throttling", counter);

                    // Use a smaller wait time with a cap to avoid excessive delays
                    int waitTime = Math.Min(timeToGo, 15);
                    _logger?.LogInformation("Waiting {WaitTime} seconds before continuing", waitTime);
                    System.Threading.Thread.Sleep(1000 * waitTime);
                }
                else if (counter > 250)
                {
                    // Add a small delay when getting close to the limit
                    _logger?.LogInformation("Rate limit count approaching limit ({Count}/300), adding small delay", counter);
                    System.Threading.Thread.Sleep(500);
                }
            }
        }

        /// <summary>
        /// Performs a PUT request with standardized rate limit handling
        /// </summary>
        internal string JsonPutString(string url, string apiKey, JObject jsonData)
        {
            int attempts = 1;

            while (attempts <= 5) // Match MAX_RETRY_ATTEMPTS
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    var content = new StringContent(jsonData.ToString(), Encoding.UTF8, "application/json");

                    try
                    {
                        var response = client.PutAsync(url, content).Result;

                        if (response.IsSuccessStatusCode)
                        {
                            return response.Content.ReadAsStringAsync().Result;
                        }
                        else
                        {
                            var responseContent = response.Content.ReadAsStringAsync().Result;
                            _lastResponseContent = responseContent;

                            // Handle rate limiting
                            if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                _logger?.LogWarning("Rate limit exceeded (PUT) - Attempt {Attempt}", attempts);

                                string retryAfter = string.Empty;
                                if (response.Headers.TryGetValues("Retry-After", out var values))
                                {
                                    retryAfter = values.First();
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    responseContent,
                                    attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                if (!rateLimitResult.shouldRetry)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                    return string.Empty;
                                }

                                attempts++;
                            }
                            else
                            {
                                _logger?.LogError("Failed to update. Status Code: {StatusCode}, Response: {Response}",
                                    response.StatusCode, responseContent);
                                return string.Empty;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Exception in JsonPutString for URL {Url}", url);
                        return string.Empty;
                    }
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// Performs a GET request with standardized rate limit handling
        /// </summary>
        internal string JsonGetString(string url, string apiKey)
        {
            int attempts = 1;

            while (attempts <= 5) // Match MAX_RETRY_ATTEMPTS
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

                    try
                    {
                        var response = client.GetAsync(url).Result;

                        if (response.IsSuccessStatusCode)
                        {
                            return response.Content.ReadAsStringAsync().Result;
                        }
                        else
                        {
                            var responseContent = response.Content.ReadAsStringAsync().Result;
                            _lastResponseContent = responseContent;

                            // Handle rate limiting
                            if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                _logger?.LogWarning("Rate limit exceeded (GET) - Attempt {Attempt}", attempts);

                                string retryAfter = string.Empty;
                                if (response.Headers.TryGetValues("Retry-After", out var values))
                                {
                                    retryAfter = values.First();
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    responseContent,
                                    attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                if (!rateLimitResult.shouldRetry)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                    return string.Empty;
                                }

                                attempts++;
                            }
                            else
                            {
                                _logger?.LogWarning("Failed to get resource. Status Code: {StatusCode}", response.StatusCode);
                                return string.Empty;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Exception in JsonGetString for URL {Url}", url);
                        return string.Empty;
                    }
                }
            }

            return string.Empty;
        }
    }
}
// spell-checker: ignore: resp, emite
