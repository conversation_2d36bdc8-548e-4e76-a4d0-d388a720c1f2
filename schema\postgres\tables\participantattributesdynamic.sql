CREATE TABLE IF NOT EXISTS participantattributesdynamic (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50) NOT NULL,
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT participantattributesdynamic_new_pkey PRIMARY KEY (keyid, conversationstartdate)
) PARTITION BY RANGE (conversationstartdate);

CREATE INDEX IF NOT EXISTS partattirbconv ON participantattributesdynamic USING btree (
    conversationid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS partattribend ON participantattributesdynamic USING btree (conversationenddate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS partattribendltc ON participantattributesdynamic USING btree (conversationenddateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS partattribstart ON participantattributesdynamic USING btree (conversationstartdate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS partattribstartltc ON participantattributesdynamic USING btree (conversationstartdateltc ASC NULLS LAST);

ALTER TABLE IF EXISTS partattrib_2020_01 RENAME TO participantattributesdynamic_p2020_01;
ALTER TABLE IF EXISTS partattrib_2020_02 RENAME TO participantattributesdynamic_p2020_02;
ALTER TABLE IF EXISTS partattrib_2020_03 RENAME TO participantattributesdynamic_p2020_03;
ALTER TABLE IF EXISTS partattrib_2020_04 RENAME TO participantattributesdynamic_p2020_04;
ALTER TABLE IF EXISTS partattrib_2020_05 RENAME TO participantattributesdynamic_p2020_05;
ALTER TABLE IF EXISTS partattrib_2020_06 RENAME TO participantattributesdynamic_p2020_06;
ALTER TABLE IF EXISTS partattrib_2020_07 RENAME TO participantattributesdynamic_p2020_07;
ALTER TABLE IF EXISTS partattrib_2020_08 RENAME TO participantattributesdynamic_p2020_08;
ALTER TABLE IF EXISTS partattrib_2020_09 RENAME TO participantattributesdynamic_p2020_09;
ALTER TABLE IF EXISTS partattrib_2020_10 RENAME TO participantattributesdynamic_p2020_10;
ALTER TABLE IF EXISTS partattrib_2020_11 RENAME TO participantattributesdynamic_p2020_11;
ALTER TABLE IF EXISTS partattrib_2020_12 RENAME TO participantattributesdynamic_p2020_12;
ALTER TABLE IF EXISTS partattrib_2021_01 RENAME TO participantattributesdynamic_p2021_01;
ALTER TABLE IF EXISTS partattrib_2021_02 RENAME TO participantattributesdynamic_p2021_02;
ALTER TABLE IF EXISTS partattrib_2021_03 RENAME TO participantattributesdynamic_p2021_03;
ALTER TABLE IF EXISTS partattrib_2021_04 RENAME TO participantattributesdynamic_p2021_04;
ALTER TABLE IF EXISTS partattrib_2021_05 RENAME TO participantattributesdynamic_p2021_05;
ALTER TABLE IF EXISTS partattrib_2021_06 RENAME TO participantattributesdynamic_p2021_06;
ALTER TABLE IF EXISTS partattrib_2021_07 RENAME TO participantattributesdynamic_p2021_07;
ALTER TABLE IF EXISTS partattrib_2021_08 RENAME TO participantattributesdynamic_p2021_08;
ALTER TABLE IF EXISTS partattrib_2021_09 RENAME TO participantattributesdynamic_p2021_09;
ALTER TABLE IF EXISTS partattrib_2021_10 RENAME TO participantattributesdynamic_p2021_10;
ALTER TABLE IF EXISTS partattrib_2021_11 RENAME TO participantattributesdynamic_p2021_11;
ALTER TABLE IF EXISTS partattrib_2021_12 RENAME TO participantattributesdynamic_p2021_12;
ALTER TABLE IF EXISTS partattrib_2022_01 RENAME TO participantattributesdynamic_p2022_01;
ALTER TABLE IF EXISTS partattrib_2022_02 RENAME TO participantattributesdynamic_p2022_02;
ALTER TABLE IF EXISTS partattrib_2022_03 RENAME TO participantattributesdynamic_p2022_03;
ALTER TABLE IF EXISTS partattrib_2022_04 RENAME TO participantattributesdynamic_p2022_04;
ALTER TABLE IF EXISTS partattrib_2022_05 RENAME TO participantattributesdynamic_p2022_05;
ALTER TABLE IF EXISTS partattrib_2022_06 RENAME TO participantattributesdynamic_p2022_06;
ALTER TABLE IF EXISTS partattrib_2022_07 RENAME TO participantattributesdynamic_p2022_07;
ALTER TABLE IF EXISTS partattrib_2022_08 RENAME TO participantattributesdynamic_p2022_08;
ALTER TABLE IF EXISTS partattrib_2022_09 RENAME TO participantattributesdynamic_p2022_09;
ALTER TABLE IF EXISTS partattrib_2022_10 RENAME TO participantattributesdynamic_p2022_10;
ALTER TABLE IF EXISTS partattrib_2022_11 RENAME TO participantattributesdynamic_p2022_11;
ALTER TABLE IF EXISTS partattrib_2022_12 RENAME TO participantattributesdynamic_p2022_12;

-- Fix participant attributes data corruption from threading race conditions
DO $$
BEGIN
    IF csg_table_exists('participantattributesdynamic') = 1 THEN
        UPDATE participantattributesdynamic
        SET conversationid = keyid
        WHERE conversationid != keyid;
    END IF;
END $$;
