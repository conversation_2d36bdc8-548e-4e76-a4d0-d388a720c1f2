# PowerShell script to replace complex logging with simple Console.WriteLine
$filePath = "GenesysCloudUtils/SegmentData.cs"
$content = Get-Content $filePath -Raw

# Replace _logger?.LogInformation patterns
$content = $content -replace '_logger\?\\.LogInformation\(', 'Console.WriteLine('

# Replace _logger?.LogWarning patterns
$content = $content -replace '_logger\?\\.LogWarning\(', 'Console.WriteLine('

# Replace _logger?.LogError patterns
$content = $content -replace '_logger\?\\.LogError\(', 'Console.WriteLine('

# Replace _logger?.LogDebug patterns
$content = $content -replace '_logger\?\\.LogDebug\(', 'Console.WriteLine('

# Replace _logger?.LogTrace patterns
$content = $content -replace '_logger\?\\.LogTrace\(', 'Console.WriteLine('

Set-Content $filePath $content
Write-Host "Logging patterns replaced successfully"
