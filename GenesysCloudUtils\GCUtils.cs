﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using PureCloudPlatform.Client.V2.Client;
using PureCloudPlatform.Client.V2.Extensions;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class GCUtils
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;

        public DataSet GCControlData { get; set; }
        private string _genesysBaseUri;
        private readonly ILogger? _logger;





        public GCUtils()
        {
        }

        public GCUtils(ILogger? logger)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            UCAUtils = new StandardUtils.Utils();

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            // Reduced log noise - moved to debug level
            System.Diagnostics.Debug.WriteLine("Enabling Encryption");
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            System.Diagnostics.Debug.WriteLine("Creating Admin Data");
            CreateGCAdminData();
            System.Diagnostics.Debug.WriteLine("Getting Key");

            bool authSuccess = GetGCAPIKey();
            if (!authSuccess)
            {
                throw new InvalidOperationException("Failed to authenticate with Genesys Cloud. This may be due to rate limiting or invalid credentials.");
            }

            System.Diagnostics.Debug.WriteLine("Retrieved Key");
        }

#nullable enable
        public GCUtils Initialize(CSG.Adapter.Configuration.GenesysApi genesysOptions)
        {
            if (genesysOptions.ClientId is null || genesysOptions.ClientSecret is null)
                throw new ArgumentNullException(nameof(genesysOptions), "Genesys credentials cannot be null");
            UCAUtils = new StandardUtils.Utils();

            bool authSuccess = GetGCAPIKey(
                genesysOptions.ClientId,
                new Secret(genesysOptions.ClientId, genesysOptions.ClientSecret).PlainText,
                genesysOptions.Endpoint?.ToString());

            if (!authSuccess)
            {
                throw new InvalidOperationException("Failed to authenticate with Genesys Cloud using provided credentials. This may be due to rate limiting or invalid credentials.");
            }

            return this;
        }
#nullable restore

        public string OAuthKey(string userID, string password, string URL)
        {
            string oAuth = string.Empty;
            // Transform API URL to login URL for authentication
            string loginUrl = URL.Replace("api.", "login.");
            // Console.WriteLine("Using login URL: {0}", loginUrl);

            // Create rate limit handler for structured rate limiting
            var rateLimitHandler = new GenesysCloudUtils.RateLimitHandler(_logger, this);

            int maxRetries = 10; // Use same max as RateLimitHandler
            int attempt = 1;

            while (attempt <= maxRetries)
            {
                try
                {
                    PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.RestClient.BaseUrl = new Uri(loginUrl);
                    var accessTokenInfo = PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.PostToken(userID, password);
                    oAuth = accessTokenInfo.AccessToken.ToString();
                    return oAuth;
                }
                catch (PureCloudPlatform.Client.V2.Client.ApiException ex) when (IsRateLimitException(ex))
                {
                    if (attempt >= maxRetries)
                    {
                        _logger?.LogError(ex, "Rate limit exceeded after {MaxRetries} attempts during authentication, giving up", maxRetries);
                        throw;
                    }

                    // Use the structured rate limit handler for consistent behavior
                    var rateLimitResult = rateLimitHandler.HandleRateLimit(
                        ExtractRetryAfterFromMessage(ex.Message)?.ToString() ?? string.Empty,
                        ex.Message,
                        attempt,
                        string.Empty); // No existing API key during authentication

                    if (!rateLimitResult.shouldRetry)
                    {
                        _logger?.LogError(ex, "Rate limiting exceeded retry limit during authentication after {Attempts} attempts", attempt);
                        throw;
                    }

                    attempt++;
                }
            }

            throw new InvalidOperationException($"Failed to obtain OAuth token after {maxRetries} attempts");
        }

        /// <summary>
        /// Determines if an ApiException is a rate limiting exception
        /// </summary>
        private bool IsRateLimitException(PureCloudPlatform.Client.V2.Client.ApiException ex)
        {
            // Check multiple indicators of rate limiting
            return ex.ErrorCode == 429 ||
                   ex.Message.Contains("rate limit exceeded") ||
                   ex.Message.Contains("retry after:");
        }

        /// <summary>
        /// Extracts retry-after seconds from Genesys Cloud error message
        /// </summary>
        private int? ExtractRetryAfterFromMessage(string errorMessage)
        {
            try
            {
                // Look for "retry after: X" pattern in the error message
                var match = System.Text.RegularExpressions.Regex.Match(errorMessage, @"retry after:\s*(\d+)");
                if (match.Success && int.TryParse(match.Groups[1].Value, out int retryAfter))
                {
                    return retryAfter;
                }
            }
            catch
            {
                // If parsing fails, return null to use default
            }
            return null;
        }



        private void CreateGCAdminData()
        {
            GCControlData = new DataSet();
            var settings = new DataTable("GCControlData");
            settings.Columns.Add("GC_USERId", typeof(System.String));
            settings.Columns.Add("GC_Secret", typeof(System.String));
            settings.Columns.Add("GC_URL", typeof(System.String));
            var row = settings.NewRow();
            row["GC_USERId"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID");
            row["GC_Secret"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET");
            row["GC_URL"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL");
            settings.Rows.Add(row);
            GCControlData.Tables.Add(settings);
        }

        public bool GetGCAPIKey()
        {
            try
            {
                GetGCAPIKey(
                    CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID"),
                    CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET"),
                    CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL"));

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to get API key: {0}", ex.Message);
                return false;
            }
        }

        public bool GetGCAPIKey(string userId, string password, string uri)
        {
            try
            {
                _genesysBaseUri = uri.TrimEnd('/');
                GCApiKey = OAuthKey(userId, password, uri);

                GCApiKeyLastUpdate = DateTime.Now;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed to get API key for user: {0}", ex.Message);
                return false;
            }
        }



#nullable enable
        public CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me GetOrganization()
        {
            var apiMethod = HttpMethod.Post;
            var apiEndpoint = "/api/v2/organizations/me";

            GenesysCloudUtils.JsonUtils ju = new(_logger);
            string? apiResult = null;
            try
            {
                apiResult = ju.JsonReturnString(_genesysBaseUri + apiEndpoint, GCApiKey);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calling Genesys Cloud API {ApiMethod} {ApiEndpoint}. Response: {ApiResult}",
                    apiMethod, apiEndpoint, apiResult ?? "null");
                throw;
            }
            if (apiResult == null)
                throw new InvalidDataException(
                    $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
                );

            // Check if the response is an error response (from rate limiting or other issues)
            if (apiResult.Contains("\"error\": true"))
            {
                try
                {
                    var errorResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(apiResult);
                    string errorMessage = errorResponse?.message ?? "Unknown error";
                    string statusCode = errorResponse?.statusCode ?? "Unknown";

                    _logger?.LogError("API call to {ApiMethod} {ApiEndpoint} returned error response: {ErrorMessage} (Status: {StatusCode})",
                        apiMethod, apiEndpoint, errorMessage, statusCode);

                    // For rate limiting errors, throw a specific exception that can be handled by the caller
                    if (statusCode == "TooManyRequests")
                    {
                        throw new InvalidOperationException($"Rate limiting exceeded retry limit when calling {apiMethod} {apiEndpoint}: {errorMessage}");
                    }
                    else
                    {
                        throw new InvalidDataException($"API error when calling {apiMethod} {apiEndpoint}: {errorMessage} (Status: {statusCode})");
                    }
                }
                catch (Newtonsoft.Json.JsonException)
                {
                    // If we can't parse the error response, fall through to the original error handling
                    _logger?.LogError("Failed to parse error response from {ApiMethod} {ApiEndpoint}: {ApiResult}", apiMethod, apiEndpoint, apiResult);
                    throw new InvalidDataException($"Invalid error response from Genesys Cloud when calling {apiMethod} {apiEndpoint}");
                }
            }

            CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me? gcOrganisation = null;
            try
            {
                gcOrganisation = Newtonsoft.Json.JsonConvert.DeserializeObject<CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me>(apiResult);
            }
            catch (Newtonsoft.Json.JsonException jsonEx)
            {
                _logger?.LogError(jsonEx, "JSON deserialization failed when calling {ApiMethod} {ApiEndpoint}. Response: {ApiResult}",
                    apiMethod, apiEndpoint, apiResult);
                throw new InvalidDataException($"Failed to parse organization data from Genesys Cloud when calling {apiMethod} {apiEndpoint}: {jsonEx.Message}");
            }

            if (gcOrganisation == null)
                throw new InvalidDataException(
                    $"Failed to parse organization data from Genesys Cloud when calling {apiMethod} {apiEndpoint} - deserialization returned null"
                );

            return gcOrganisation;
        }
#nullable restore
    }
}
