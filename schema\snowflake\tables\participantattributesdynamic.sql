CREATE TABLE IF NOT EXISTS participantattributesdynamic (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50) NOT NULL,
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT participantattributesdynamic_new_pkey PRIMARY KEY (keyid, conversationstartdate)
) ;

-- Fix participant attributes data corruption from threading race conditions
UPDATE participantattributesdynamic
SET conversationid = keyid
WHERE conversationid != keyid;